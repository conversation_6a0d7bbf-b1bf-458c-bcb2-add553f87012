<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\V1\TaskGroupController;
use App\Http\Controllers\Api\V1\MonitorTaskController;
use App\Http\Controllers\Api\V1\AlertRuleController;
use App\Http\Controllers\Api\V1\ProductController;
use App\Http\Controllers\Api\CompetitorMetricsController;
use App\Http\Controllers\Api\ProductSearchController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// --- Public Routes ---

// Performance monitoring routes
Route::prefix('performance')->group(function () {
    Route::get('/stats', [PerformanceMonitorController::class, 'getTaskPerformanceStats']);
    Route::get('/execution-times', [PerformanceMonitorController::class, 'getTaskExecutionTimes']);
    Route::get('/queue-analysis', [PerformanceMonitorController::class, 'getQueueDepthAnalysis']);
    Route::get('/retry-analysis', [PerformanceMonitorController::class, 'getTaskRetryAnalysis']);
    Route::get('/platform-comparison', [PerformanceMonitorController::class, 'getPlatformPerformanceComparison']);
    Route::get('/bottleneck-analysis', [PerformanceMonitorController::class, 'getTaskBottleneckAnalysis']);
    Route::get('/snapshot', [PerformanceMonitorController::class, 'getTaskPerformanceSnapshot']);
    Route::get('/dashboard', [PerformanceMonitorController::class, 'getDashboardData']);
    Route::get('/trends', [PerformanceMonitorController::class, 'getPerformanceTrends']);
    Route::post('/record/task-execution', [PerformanceMonitorController::class, 'recordTaskExecution']);
    Route::post('/record/task-retry', [PerformanceMonitorController::class, 'recordTaskRetry']);
    Route::post('/record/queue-depth', [PerformanceMonitorController::class, 'recordQueueDepth']);
    Route::post('/record/hourly-metrics', [PerformanceMonitorController::class, 'recordHourlyMetrics']);
});


// --- Authenticated Routes ---

Route::middleware('auth:sanctum')->group(function () {
    Route::get('/user', function (Request $request) {
        return $request->user();
    });

    // Resource Routes
    Route::apiResource('categories', CategoryController::class);
    Route::apiResource('products', ProductController::class);
    Route::apiResource('monitor-tasks', MonitorTaskController::class);
    Route::apiResource('task-groups', TaskGroupController::class);
    Route::apiResource('alert-rules', AlertRuleController::class);
    Route::apiResource('competitor-metrics', CompetitorMetricsController::class);

    // Product Routes
    Route::get('/products/search', [ProductSearchController::class, 'index'])->name('products.search');
    Route::get('/products/{product}/skus', function(\App\Models\Product $product) {
        return response()->json([
            'success' => true,
            'data' => $product->skus()->select('id', 'sku_code', 'title', 'price')->get(),
            'message' => '产品SKU获取成功'
        ]);
    })->name('products.skus');

    // Official Guide Price Routes
    Route::prefix('official-guide-prices')->name('official-guide-prices.')->group(function () {
        Route::get('stats', [OfficialGuidePriceController::class, 'stats'])->name('stats');
        Route::post('set-single', [OfficialGuidePriceController::class, 'setSingle'])->name('set-single');
        Route::post('set-batch', [OfficialGuidePriceController::class, 'setBatch'])->name('set-batch');
        Route::post('import-csv', [OfficialGuidePriceController::class, 'importCsv'])->name('import-csv');
        Route::post('calculate-auto', [OfficialGuidePriceController::class, 'calculateAuto'])->name('calculate-auto');
        Route::post('batch-calculate-auto', [OfficialGuidePriceController::class, 'batchCalculateAuto'])->name('batch-calculate-auto');
        Route::get('skus-needing', [OfficialGuidePriceController::class, 'getSkusNeeding'])->name('skus-needing');
        Route::get('download-template', [OfficialGuidePriceController::class, 'downloadTemplate'])->name('download-template');
        Route::delete('clear', [OfficialGuidePriceController::class, 'clear'])->name('clear');
    });

    // Alert Rule Routes
    Route::post('/alert-rules/batch-delete', [AlertRuleController::class, 'batchDestroy'])->name('alert-rules.batch-destroy');
    Route::patch('/alert-rules/{alertRule}/toggle-status', [AlertRuleController::class, 'toggleStatus'])->name('alert-rules.toggle-status');
    Route::get('/alert-rules/stats/overview', [AlertRuleController::class, 'stats'])->name('alert-rules.stats');
    Route::get('/alert-rules/options/all', [AlertRuleController::class, 'options'])->name('alert-rules.options');
    Route::post('/alert-rules/validate', [AlertRuleController::class, 'validateRule']);
    Route::get('/alert-rules/config/templates', [AlertRuleController::class, 'getConfigTemplates']);
    Route::post('/alert-rules/test', [AlertRuleController::class, 'test']);

    // Alert Log Routes
    Route::get('/alert-logs', [AlertLogController::class, 'index']);

    // Task Group Routes
    Route::get('/task-groups/search', [TaskGroupController::class, 'search']);
    Route::get('/task-groups/unassigned-tasks', [TaskGroupController::class, 'unassignedTasks'])->name('task-groups.unassigned-tasks');
    Route::get('/task-groups/distribution-analysis', [TaskGroupController::class, 'distributionAnalysis'])->name('task-groups.distribution-analysis');
    Route::get('/task-groups/comparison-report', [TaskGroupController::class, 'getComparisonReport'])->name('task-groups.comparison-report');
    Route::post('/task-groups/auto-group', [TaskGroupController::class, 'autoGroup'])->name('task-groups.auto-group');
    Route::post('/task-groups/merge', [TaskGroupController::class, 'merge'])->name('task-groups.merge');
    Route::get('/task-groups/{taskGroup}', [TaskGroupController::class, 'show'])->name('task-groups.show');
    Route::put('/task-groups/{taskGroup}', [TaskGroupController::class, 'update'])->name('task-groups.update');
    Route::delete('/task-groups/{taskGroup}', [TaskGroupController::class, 'destroy'])->name('task-groups.destroy');
    Route::post('/task-groups/{taskGroup}/assign-tasks', [TaskGroupController::class, 'assignTasks'])->name('task-groups.assign-tasks');
    Route::post('/task-groups/{taskGroup}/remove-tasks', [TaskGroupController::class, 'removeTasks'])->name('task-groups.remove-tasks');
    Route::post('/task-groups/{taskGroup}/duplicate', [TaskGroupController::class, 'duplicate'])->name('task-groups.duplicate');
    Route::get('/task-groups/{taskGroup}/statistics', [TaskGroupController::class, 'statistics'])->name('task-groups.statistics');
    Route::get('/task-groups/{taskGroup}/report', [TaskGroupController::class, 'getReport'])->name('task-groups.report');
    Route::get('/task-groups/{taskGroup}/report-summary', [TaskGroupController::class, 'getReportSummary'])->name('task-groups.report-summary');
    Route::get('/task-groups/{taskGroup}/export-report', [TaskGroupController::class, 'exportReport'])->name('task-groups.export-report');
    Route::get('/task-groups/{id}/aggregate-report', [GroupReportingController::class, 'show'])->name('task-groups.aggregate-report');

    // Competitor Search Routes
    Route::prefix('competitors')->name('competitors.')->middleware('competitor.monitoring')->group(function () {
        Route::post('/search', [CompetitorController::class, 'search'])->name('search');
        Route::post('/search-similar', [CompetitorController::class, 'searchSimilar'])->name('search-similar');
        Route::post('/find-similar/{productId}', [CompetitorController::class, 'findSimilar'])->name('find-similar');
        Route::get('/suggestions', [CompetitorController::class, 'suggestions'])->name('suggestions');
        Route::post('/export', [CompetitorController::class, 'exportResults'])->name('export');
        Route::post('/clear-cache', [CompetitorController::class, 'clearCache'])->name('clear-cache');
        Route::get('/stats', [CompetitorController::class, 'getStats'])->name('stats');
    });
});

Route::prefix('v1')->group(function () {
    Route::apiResource('task-groups', TaskGroupController::class);
    Route::apiResource('monitor-tasks', MonitorTaskController::class);
    Route::apiResource('alert-rules', AlertRuleController::class);
    Route::apiResource('competitor-metrics', CompetitorMetricsController::class);
    
    Route::prefix('products/{product}')->group(function () {
        Route::apiResource('skus', ProductController::class)->except(['store', 'update', 'destroy']);
    });
}); 