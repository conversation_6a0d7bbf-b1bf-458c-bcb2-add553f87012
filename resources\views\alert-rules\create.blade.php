@extends('layouts.app')

@section('title', '创建警报规则')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="page-title">
        <i class="fas fa-plus text-primary me-2"></i>
        创建警报规则
    </h1>
    <a href="{{ route('alert-rules.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-1"></i>
        返回列表
    </a>
</div>

<div class="row">
    {{-- 主表单 --}}
    <div class="col-lg-8">
        <form id="alertRuleForm" method="POST" action="{{ route('alert-rules.store') }}">
            @csrf
            
            {{-- 基本信息 --}}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        基本信息
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    规则名称 <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control @error('name') is-invalid @enderror" 
                                       id="name" 
                                       name="name" 
                                       value="{{ old('name') }}" 
                                       placeholder="为您的警报规则起一个描述性的名称"
                                       required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">建议使用清晰描述的名称，如"商品价格上涨10%警报"</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="priority" class="form-label">优先级</label>
                                <select class="form-select @error('priority') is-invalid @enderror" 
                                        id="priority" 
                                        name="priority">
                                    <option value="low" {{ old('priority') == 'low' ? 'selected' : '' }}>低</option>
                                    <option value="medium" {{ old('priority', 'medium') == 'medium' ? 'selected' : '' }}>中</option>
                                    <option value="high" {{ old('priority') == 'high' ? 'selected' : '' }}>高</option>
                                </select>
                                @error('priority')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">描述</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" 
                                  name="description" 
                                  rows="3" 
                                  placeholder="详细描述此警报规则的用途和触发条件">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" 
                               type="checkbox" 
                               id="is_active" 
                               name="is_active" 
                               value="1" 
                               {{ old('is_active', true) ? 'checked' : '' }}>
                        <label class="form-check-label" for="is_active">
                            启用此规则
                        </label>
                    </div>
                </div>
            </div>

            {{-- 警报类型配置 --}}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        警报类型与条件
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <label for="type" class="form-label">
                            警报类型 <span class="text-danger">*</span>
                        </label>
                        <select class="form-select @error('type') is-invalid @enderror" 
                                id="type" 
                                name="type" 
                                onchange="updateConfigFields()" 
                                required>
                            <option value="">请选择警报类型</option>
                            <option value="price_increase" {{ old('type') == 'price_increase' ? 'selected' : '' }}>
                                价格上涨警报
                            </option>
                            <option value="price_decrease" {{ old('type') == 'price_decrease' ? 'selected' : '' }}>
                                价格下降警报
                            </option>
                            <option value="stock_shortage" {{ old('type') == 'stock_shortage' ? 'selected' : '' }}>
                                库存不足警报
                            </option>
                            <option value="competitor_analysis" {{ old('type') == 'competitor_analysis' ? 'selected' : '' }}>
                                竞争对手分析警报
                            </option>
                            <option value="market_trend" {{ old('type') == 'market_trend' ? 'selected' : '' }}>
                                市场趋势警报
                            </option>
                            <option value="price_deviation" {{ old('type') == 'price_deviation' ? 'selected' : '' }}>
                                价格偏差警报
                            </option>
                        </select>
                        @error('type')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">选择适合您需求的警报类型</div>
                    </div>

                    {{-- 动态配置字段 --}}
                    <div id="configFields">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            请先选择警报类型以配置具体的触发条件
                        </div>
                    </div>
                </div>
            </div>

            {{-- 通知设置 --}}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bell me-2"></i>
                        通知设置
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">通知方式</label>
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="notify_email" 
                                           name="notification_config[email]" 
                                           value="1"
                                           {{ old('notification_config.email') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="notify_email">
                                        <i class="fas fa-envelope me-1"></i>邮件通知
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="notify_system" 
                                           name="notification_config[system]" 
                                           value="1"
                                           {{ old('notification_config.system', true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="notify_system">
                                        <i class="fas fa-desktop me-1"></i>站内通知
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="frequency" class="form-label">通知频率</label>
                                <select class="form-select @error('frequency') is-invalid @enderror" 
                                        id="frequency" 
                                        name="frequency">
                                    <option value="immediate" {{ old('frequency', 'immediate') == 'immediate' ? 'selected' : '' }}>
                                        立即通知
                                    </option>
                                    <option value="hourly" {{ old('frequency') == 'hourly' ? 'selected' : '' }}>
                                        每小时一次
                                    </option>
                                    <option value="daily" {{ old('frequency') == 'daily' ? 'selected' : '' }}>
                                        每天一次
                                    </option>
                                    <option value="weekly" {{ old('frequency') == 'weekly' ? 'selected' : '' }}>
                                        每周一次
                                    </option>
                                </select>
                                @error('frequency')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {{-- 提交按钮 --}}
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{{ route('alert-rules.index') }}" class="btn btn-outline-secondary me-md-2">
                    <i class="fas fa-times me-1"></i>取消
                </a>
                <button type="submit" class="btn btn-primary" id="submitBtn">
                    <i class="fas fa-save me-1"></i>
                    <span class="btn-text">创建规则</span>
                    <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                </button>
            </div>
        </form>
    </div>

    {{-- 帮助信息 --}}
    <div class="col-lg-4">
        <div class="card sticky-top" style="top: 100px;">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>
                    配置指南
                </h6>
            </div>
            <div class="card-body">
                <div id="helpContent">
                    <h6>警报类型说明</h6>
                    <ul class="small">
                        <li><strong>价格上涨/下降：</strong>监控商品价格变化幅度</li>
                        <li><strong>库存不足：</strong>监控商品库存数量</li>
                        <li><strong>竞争对手分析：</strong>监控竞争对手价格动态</li>
                        <li><strong>市场趋势：</strong>监控整体市场价格趋势</li>
                        <li><strong>价格偏差：</strong>监控价格与预期的偏差</li>
                    </ul>
                    
                    <h6 class="mt-3">最佳实践</h6>
                    <ul class="small">
                        <li>使用清晰的规则名称便于管理</li>
                        <li>设置合理的阈值避免过多通知</li>
                        <li>根据重要性选择适当的优先级</li>
                        <li>定期检查和更新规则配置</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

{{-- 动态配置模板 --}}
<div id="configTemplates" style="display: none;">
    {{-- 价格变化模板 --}}
    <div id="price-change-template">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="threshold_percentage" class="form-label">
                        阈值百分比 (%) <span class="text-danger">*</span>
                    </label>
                    <input type="number" 
                           class="form-control" 
                           id="threshold_percentage" 
                           name="threshold_percentage" 
                           min="0" 
                           max="100" 
                           step="0.1" 
                           placeholder="例如：10"
                           value="{{ old('threshold_percentage') }}">
                    <div class="form-text">价格变化超过此百分比时触发警报</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="threshold_value" class="form-label">
                        阈值金额 (¥)
                    </label>
                    <input type="number" 
                           class="form-control" 
                           id="threshold_value" 
                           name="threshold_value" 
                           min="0" 
                           step="0.01" 
                           placeholder="例如：50.00"
                           value="{{ old('threshold_value') }}">
                    <div class="form-text">可选：价格变化超过此金额时触发</div>
                </div>
            </div>
        </div>
        <div class="mb-3">
            <label for="time_window" class="form-label">时间窗口</label>
            <select class="form-select" id="time_window" name="time_window">
                <option value="1hour">1小时内</option>
                <option value="24hours" selected>24小时内</option>
                <option value="7days">7天内</option>
                <option value="30days">30天内</option>
            </select>
            <div class="form-text">在此时间窗口内检测价格变化</div>
        </div>
    </div>

    {{-- 库存不足模板 --}}
    <div id="stock-shortage-template">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="stock_threshold" class="form-label">
                        库存阈值 <span class="text-danger">*</span>
                    </label>
                    <input type="number" 
                           class="form-control" 
                           id="stock_threshold" 
                           name="threshold_value" 
                           min="0" 
                           placeholder="例如：10"
                           value="{{ old('threshold_value') }}">
                    <div class="form-text">库存数量低于此值时触发警报</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="check_frequency" class="form-label">检查频率</label>
                    <select class="form-select" id="check_frequency" name="check_frequency">
                        <option value="5min">每5分钟</option>
                        <option value="15min">每15分钟</option>
                        <option value="1hour" selected>每小时</option>
                        <option value="6hours">每6小时</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    {{-- 竞争对手分析模板 --}}
    <div id="competitor-analysis-template">
        <div class="mb-3">
            <label for="competitor_threshold" class="form-label">
                价格差异阈值 (%) <span class="text-danger">*</span>
            </label>
            <input type="number" 
                   class="form-control" 
                   id="competitor_threshold" 
                   name="threshold_percentage" 
                   min="0" 
                   step="0.1" 
                   placeholder="例如：5"
                   value="{{ old('threshold_percentage') }}">
            <div class="form-text">当竞争对手价格差异超过此百分比时触发</div>
        </div>
        <div class="form-check">
            <input class="form-check-input" 
                   type="checkbox" 
                   id="monitor_new_competitors" 
                   name="monitor_new_competitors" 
                   value="1">
            <label class="form-check-label" for="monitor_new_competitors">
                监控新出现的竞争对手
            </label>
        </div>
    </div>

    {{-- 市场趋势模板 --}}
    <div id="market-trend-template">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="trend_threshold" class="form-label">
                        趋势变化阈值 (%) <span class="text-danger">*</span>
                    </label>
                    <input type="number" 
                           class="form-control" 
                           id="trend_threshold" 
                           name="threshold_percentage" 
                           min="0" 
                           step="0.1" 
                           placeholder="例如：15"
                           value="{{ old('threshold_percentage') }}">
                    <div class="form-text">市场整体价格趋势变化超过此值时触发</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="trend_period" class="form-label">趋势分析周期</label>
                    <select class="form-select" id="trend_period" name="trend_period">
                        <option value="7days">7天</option>
                        <option value="14days" selected>14天</option>
                        <option value="30days">30天</option>
                        <option value="90days">90天</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    {{-- 价格偏差模板 --}}
    <div id="price-deviation-template">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="expected_price" class="form-label">
                        预期价格 (¥) <span class="text-danger">*</span>
                    </label>
                    <input type="number" 
                           class="form-control" 
                           id="expected_price" 
                           name="expected_price" 
                           min="0" 
                           step="0.01" 
                           placeholder="例如：99.99"
                           value="{{ old('expected_price') }}">
                    <div class="form-text">设定的预期价格</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="deviation_threshold" class="form-label">
                        偏差阈值 (%) <span class="text-danger">*</span>
                    </label>
                    <input type="number" 
                           class="form-control" 
                           id="deviation_threshold" 
                           name="threshold_percentage" 
                           min="0" 
                           step="0.1" 
                           placeholder="例如：5"
                           value="{{ old('threshold_percentage') }}">
                    <div class="form-text">实际价格偏离预期价格超过此百分比时触发</div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
function updateConfigFields() {
    const typeSelect = document.getElementById('type');
    const configFields = document.getElementById('configFields');
    const helpContent = document.getElementById('helpContent');
    
    // 清空现有配置
    configFields.innerHTML = '';
    
    const selectedType = typeSelect.value;
    
    if (!selectedType) {
        configFields.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                请先选择警报类型以配置具体的触发条件
            </div>
        `;
        return;
    }
    
    // 根据类型加载对应的配置模板
    let templateId = '';
    let helpText = '';
    
    switch(selectedType) {
        case 'price_increase':
        case 'price_decrease':
            templateId = 'price-change-template';
            helpText = `
                <h6>${selectedType === 'price_increase' ? '价格上涨' : '价格下降'}警报配置</h6>
                <ul class="small">
                    <li>设置百分比阈值：如10%表示价格变化10%时触发</li>
                    <li>可选金额阈值：设置绝对金额变化阈值</li>
                    <li>时间窗口：检测该时间段内的价格变化</li>
                    <li>建议：重要商品设置较小阈值(5-10%)</li>
                </ul>
            `;
            break;
        case 'stock_shortage':
            templateId = 'stock-shortage-template';
            helpText = `
                <h6>库存不足警报配置</h6>
                <ul class="small">
                    <li>库存阈值：低于此数量时触发警报</li>
                    <li>检查频率：系统检查库存的频率</li>
                    <li>建议：热销商品设置较高阈值</li>
                    <li>注意：频繁检查会增加系统负载</li>
                </ul>
            `;
            break;
        case 'competitor_analysis':
            templateId = 'competitor-analysis-template';
            helpText = `
                <h6>竞争对手分析警报配置</h6>
                <ul class="small">
                    <li>价格差异：竞争对手价格与我方价格的差异</li>
                    <li>新竞争对手：监控是否有新的竞争对手出现</li>
                    <li>建议：设置5-10%的差异阈值</li>
                    <li>用途：及时调整价格策略</li>
                </ul>
            `;
            break;
        case 'market_trend':
            templateId = 'market-trend-template';
            helpText = `
                <h6>市场趋势警报配置</h6>
                <ul class="small">
                    <li>趋势变化：整体市场价格的变化幅度</li>
                    <li>分析周期：计算趋势的时间范围</li>
                    <li>建议：周期越长趋势越稳定</li>
                    <li>用途：把握市场整体走向</li>
                </ul>
            `;
            break;
        case 'price_deviation':
            templateId = 'price-deviation-template';
            helpText = `
                <h6>价格偏差警报配置</h6>
                <ul class="small">
                    <li>预期价格：您认为合理的商品价格</li>
                    <li>偏差阈值：实际价格偏离预期的百分比</li>
                    <li>建议：根据商品价格波动性设置</li>
                    <li>用途：监控价格是否在合理范围内</li>
                </ul>
            `;
            break;
    }
    
    if (templateId) {
        const template = document.getElementById(templateId);
        if (template) {
            configFields.innerHTML = template.innerHTML;
            helpContent.innerHTML = helpText;
        }
    }
}

// 表单提交处理
document.getElementById('alertRuleForm').addEventListener('submit', function() {
    const submitBtn = document.getElementById('submitBtn');
    showLoading(submitBtn);
});

// 页面加载时检查是否有选中的类型（用于编辑页面或表单验证失败时）
document.addEventListener('DOMContentLoaded', function() {
    const typeSelect = document.getElementById('type');
    if (typeSelect.value) {
        updateConfigFields();
    }
});
</script>
@endpush 