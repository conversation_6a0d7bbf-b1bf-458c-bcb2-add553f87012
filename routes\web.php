<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\UserManagementController;
use App\Http\Controllers\RoleManagementController;
use App\Http\Controllers\DataSourceController;
use App\Http\Controllers\AlertRuleController;
use App\Http\Controllers\TaskGroupController;
use App\Http\Controllers\CompetitorController;
use App\Http\Controllers\Web\DashboardController;
use App\Http\Controllers\Web\ProductSearchController as WebProductSearchController;
use App\Http\Controllers\ApiConfigurationController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// 首页
Route::get('/', function () {
    return redirect()->route('auth.login');
});

// 认证路由组
Route::prefix('auth')->name('auth.')->group(function () {
    // 注册相关路由
    Route::get('/register', [AuthController::class, 'showRegisterForm'])->name('register');
    Route::post('/register', [AuthController::class, 'register'])->name('register.submit');
    
    // 登录相关路由
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login'])->name('login.submit');
    
    // OTP登录路由
    Route::post('/send-otp-login', [AuthController::class, 'sendOtpLogin'])->name('otp.send');
    Route::post('/verify-otp-login', [AuthController::class, 'verifyOtpLogin'])->name('otp.verify');
    
    // 注销路由
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
    
    // 密码重置路由
    Route::post('/forgot-password', [AuthController::class, 'forgotPassword'])->name('password.email');
    Route::post('/reset-password', [AuthController::class, 'resetPassword'])->name('password.reset');
    
    // AJAX验证路由
    Route::post('/check-username', [AuthController::class, 'checkUsername'])->name('check.username');
    Route::post('/check-email', [AuthController::class, 'checkEmail'])->name('check.email');
    
    // 手机验证路由
    Route::post('/send-phone-verification', [AuthController::class, 'sendPhoneVerification'])->name('phone.send');
    Route::post('/verify-phone-code', [AuthController::class, 'verifyPhoneCode'])->name('phone.verify');
});

// 简化路由（不带前缀，方便直接访问）
Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
Route::post('/login', [AuthController::class, 'login']);
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
Route::post('/send-otp-login', [AuthController::class, 'sendOtpLogin']);
Route::post('/verify-otp-login', [AuthController::class, 'verifyOtpLogin']);
Route::post('/forgot-password', [AuthController::class, 'forgotPassword']);
Route::post('/reset-password', [AuthController::class, 'resetPassword']);

// 管理后台路由组（需要认证和管理员权限）
Route::prefix('admin')->name('admin.')->middleware(['auth', 'role:admin'])->group(function () {
    
    // 仪表板
    Route::get('/', function() {
        return view('admin.dashboard');
    })->name('dashboard');
    
    // 用户管理路由
    Route::prefix('users')->name('users.')->group(function () {
        Route::get('/', [UserManagementController::class, 'index'])->name('index');
        Route::get('/{user}', [UserManagementController::class, 'show'])->name('show');
        Route::get('/{user}/assign-roles', [UserManagementController::class, 'assignRoleForm'])->name('assign-roles');
        Route::post('/{user}/assign-roles', [UserManagementController::class, 'assignRoles'])->name('assign-roles.submit');
        Route::delete('/{user}/roles/{role}', [UserManagementController::class, 'revokeRole'])->name('revoke-role');
        Route::patch('/{user}/status', [UserManagementController::class, 'updateStatus'])->name('update-status');
        Route::get('/{user}/permissions', [UserManagementController::class, 'getUserPermissions'])->name('permissions');
        Route::post('/batch-action', [UserManagementController::class, 'batchAction'])->name('batch-action');
    });

    // 角色管理路由
    Route::prefix('roles')->name('roles.')->group(function () {
        Route::get('/', [RoleManagementController::class, 'index'])->name('index');
        Route::get('/create', [RoleManagementController::class, 'create'])->name('create');
        Route::post('/', [RoleManagementController::class, 'store'])->name('store');
        Route::get('/{role}', [RoleManagementController::class, 'show'])->name('show');
        Route::get('/{role}/edit', [RoleManagementController::class, 'edit'])->name('edit');
        Route::put('/{role}', [RoleManagementController::class, 'update'])->name('update');
        Route::delete('/{role}', [RoleManagementController::class, 'destroy'])->name('destroy');
        Route::get('/{role}/permissions', [RoleManagementController::class, 'permissions'])->name('permissions');
        Route::post('/{role}/permissions', [RoleManagementController::class, 'updatePermissions'])->name('permissions.update');
        Route::post('/batch-action', [RoleManagementController::class, 'batchAction'])->name('batch-action');
    });
});

// API路由组（用于AJAX请求）
Route::prefix('api/v1')->name('api.')->middleware(['auth'])->group(function () {
    
    // 用户管理API
    Route::prefix('users')->name('users.')->group(function () {
        Route::get('/', [UserManagementController::class, 'index'])->middleware('permission:manage_users');
        Route::get('/{user}', [UserManagementController::class, 'show'])->middleware('permission:view_users');
        Route::get('/{user}/permissions', [UserManagementController::class, 'getUserPermissions'])->middleware('permission:view_users');
        Route::post('/{user}/assign-roles', [UserManagementController::class, 'assignRoles'])->middleware('permission:manage_users');
        Route::patch('/{user}/status', [UserManagementController::class, 'updateStatus'])->middleware('permission:manage_users');
        Route::post('/batch-action', [UserManagementController::class, 'batchAction'])->middleware('permission:manage_users');
    });

    // 角色管理API
    Route::prefix('roles')->name('roles.')->middleware('permission:manage_roles')->group(function () {
        Route::get('/', [RoleManagementController::class, 'index']);
        Route::post('/', [RoleManagementController::class, 'store']);
        Route::get('/{role}', [RoleManagementController::class, 'show']);
        Route::put('/{role}', [RoleManagementController::class, 'update']);
        Route::delete('/{role}', [RoleManagementController::class, 'destroy']);
        Route::get('/{role}/permissions', [RoleManagementController::class, 'permissions']);
        Route::post('/{role}/permissions', [RoleManagementController::class, 'updatePermissions']);
        Route::post('/batch-action', [RoleManagementController::class, 'batchAction']);
    });
});

// 个人中心路由组（需要认证）
Route::prefix('profile')->name('profile.')->middleware(['auth'])->group(function () {
    Route::get('/', [App\Http\Controllers\ProfileController::class, 'index'])->name('index');
    Route::get('/edit', [App\Http\Controllers\ProfileController::class, 'edit'])->name('edit');
    Route::put('/update', [App\Http\Controllers\ProfileController::class, 'update'])->name('update');
    Route::get('/change-password', [App\Http\Controllers\ProfileController::class, 'showChangePassword'])->name('change-password');
    Route::put('/change-password', [App\Http\Controllers\ProfileController::class, 'changePassword'])->name('change-password.update');
    Route::get('/preferences', [App\Http\Controllers\ProfileController::class, 'showPreferences'])->name('preferences');
    Route::put('/preferences', [App\Http\Controllers\ProfileController::class, 'updatePreferences'])->name('preferences.update');
    Route::get('/activity-log', [App\Http\Controllers\ProfileController::class, 'showActivityLog'])->name('activity-log');
});

// 开发环境路由（生产环境应移除）
if (app()->environment('local')) {
    Route::get('/test', function () {
        return response()->json([
            'message' => 'E-commerce Market Dynamics Monitoring System',
            'version' => '1.0.0',
            'status' => 'running',
            'timestamp' => now()->toISOString()
        ]);
    })->name('test');
}

// 添加数据源管理路由组
Route::middleware(['auth'])->prefix('data-sources')->name('data-sources.')->group(function () {
    // 数据源管理
    Route::get('/', [App\Http\Controllers\DataSourceController::class, 'index'])->name('index');
    Route::get('/create', [App\Http\Controllers\DataSourceController::class, 'create'])->name('create');
    Route::post('/', [App\Http\Controllers\DataSourceController::class, 'store'])->name('store');
    Route::delete('/{dataSource}', [App\Http\Controllers\DataSourceController::class, 'destroy'])->name('destroy');
    Route::post('/{dataSource}/retry', [App\Http\Controllers\DataSourceController::class, 'retry'])->name('retry');
    
    // 批量导入
    Route::get('/batch-import', [App\Http\Controllers\DataSourceController::class, 'batchCreate'])->name('batch-create');
    Route::post('/batch-import', [App\Http\Controllers\DataSourceController::class, 'batchStore'])->name('batch-store');
    
    // Excel导入
    Route::get('/excel-import', [App\Http\Controllers\DataSourceController::class, 'excelCreate'])->name('excel-create');
    Route::post('/excel-import', [App\Http\Controllers\DataSourceController::class, 'excelStore'])->name('excel-store');
    Route::get('/download-template', [App\Http\Controllers\DataSourceController::class, 'downloadTemplate'])->name('download-template');
    
    // 导入历史
    Route::get('/import-history', [App\Http\Controllers\DataSourceController::class, 'importHistory'])->name('import-history');
    Route::get('/import-history/{importLog}', [App\Http\Controllers\DataSourceController::class, 'importDetail'])->name('import-detail');
    Route::post('/import-history/{importLog}/undo', [App\Http\Controllers\DataSourceController::class, 'undoImport'])->name('undo-import');
});

// 添加警报规则管理路由组
Route::middleware(['auth'])->prefix('alert-rules')->name('alert-rules.')->group(function () {
    // 警报规则管理页面
    Route::get('/', [App\Http\Controllers\AlertRuleController::class, 'indexPage'])->name('index');
    Route::get('/create', [App\Http\Controllers\AlertRuleController::class, 'createPage'])->name('create');
    Route::get('/{alertRule}/edit', [App\Http\Controllers\AlertRuleController::class, 'editPage'])->name('edit');
    
    // 批量操作
    Route::post('/bulk-action', [App\Http\Controllers\AlertRuleController::class, 'bulkAction'])->name('bulk-action');
});

// 添加任务分组管理路由组
Route::middleware(['auth'])->prefix('task-groups')->name('task-groups.')->group(function () {
    // 任务分组管理页面
    Route::get('/', [App\Http\Controllers\TaskGroupController::class, 'indexPage'])->name('index');
    Route::get('/{taskGroup}/report', [App\Http\Controllers\TaskGroupController::class, 'reportPage'])->name('report');
});

// 添加竞争对手搜索路由组
Route::middleware(['auth'])->prefix('competitors')->name('competitors.')->group(function () {
    // 竞争对手搜索页面
    Route::get('/', [CompetitorController::class, 'index'])->name('index');
});

// 添加API配置管理路由组
Route::middleware(['auth', 'role:admin'])->prefix('api-configurations')->name('api-configurations.')->group(function () {
    // 基本CRUD路由
    Route::get('/', [App\Http\Controllers\ApiConfigurationController::class, 'index'])->name('index');
    Route::get('/create', [App\Http\Controllers\ApiConfigurationController::class, 'create'])->name('create');
    Route::post('/', [App\Http\Controllers\ApiConfigurationController::class, 'store'])->name('store');
    Route::get('/{apiConfiguration}', [App\Http\Controllers\ApiConfigurationController::class, 'show'])->name('show');
    Route::get('/{apiConfiguration}/edit', [App\Http\Controllers\ApiConfigurationController::class, 'edit'])->name('edit');
    Route::put('/{apiConfiguration}', [App\Http\Controllers\ApiConfigurationController::class, 'update'])->name('update');
    Route::delete('/{apiConfiguration}', [App\Http\Controllers\ApiConfigurationController::class, 'destroy'])->name('destroy');
    
    // 额外的API配置功能
    Route::post('/{apiConfiguration}/toggle-active', [App\Http\Controllers\ApiConfigurationController::class, 'toggleActive'])->name('toggle-active');
    Route::post('/{apiConfiguration}/health-check', [App\Http\Controllers\ApiConfigurationController::class, 'healthCheck'])->name('health-check');
    Route::post('/bulk-health-check', [App\Http\Controllers\ApiConfigurationController::class, 'bulkHealthCheck'])->name('bulk-health-check');
    Route::get('/{apiConfiguration}/rate-limits', [App\Http\Controllers\ApiConfigurationController::class, 'getRateLimitStatus'])->name('rate-limits');
    Route::post('/{apiConfiguration}/reset-limits', [App\Http\Controllers\ApiConfigurationController::class, 'resetRateLimits'])->name('reset-limits');

    // 版本管理功能
    Route::get('/{apiConfiguration}/version-info', [App\Http\Controllers\ApiConfigurationController::class, 'getVersionInfo'])->name('version-info');
    Route::post('/{apiConfiguration}/create-version', [App\Http\Controllers\ApiConfigurationController::class, 'createVersion'])->name('create-version');
    Route::post('/{apiConfiguration}/deprecate', [App\Http\Controllers\ApiConfigurationController::class, 'deprecateVersion'])->name('deprecate');
    Route::post('/upgrade-version', [App\Http\Controllers\ApiConfigurationController::class, 'upgradeVersion'])->name('upgrade-version');
    Route::get('/migration-path', [App\Http\Controllers\ApiConfigurationController::class, 'getMigrationPath'])->name('migration-path');
    Route::get('/version-statistics', [App\Http\Controllers\ApiConfigurationController::class, 'getVersionStatistics'])->name('version-statistics');
});

// 系统配置管理路由
Route::prefix('system-configurations')->name('system-configurations.')->group(function () {
    // 公开配置路由（无需认证）
    Route::get('/public', [App\Http\Controllers\SystemConfigurationController::class, 'getPublicConfigurations'])->name('public');

    // 需要管理员权限的路由
    Route::middleware(['auth', 'role:admin'])->group(function () {
        Route::get('/', [App\Http\Controllers\SystemConfigurationController::class, 'index'])->name('index');
        Route::get('/category/{category}', [App\Http\Controllers\SystemConfigurationController::class, 'getByCategory'])->name('category');
        Route::post('/batch-update', [App\Http\Controllers\SystemConfigurationController::class, 'batchUpdate'])->name('batch-update');
        Route::put('/{key}', [App\Http\Controllers\SystemConfigurationController::class, 'update'])->name('update');
        Route::post('/{key}/reset', [App\Http\Controllers\SystemConfigurationController::class, 'resetToDefault'])->name('reset');
        Route::get('/export', [App\Http\Controllers\SystemConfigurationController::class, 'exportConfigurations'])->name('export');
        Route::post('/test-email', [App\Http\Controllers\SystemConfigurationController::class, 'testEmailConfiguration'])->name('test-email');
    });
});

// 监控和日志管理路由
Route::prefix('monitoring')->name('monitoring.')->middleware(['auth', 'role:admin'])->group(function () {
    Route::get('/', [App\Http\Controllers\MonitoringController::class, 'dashboard'])->name('dashboard');
    Route::get('/system-overview', [App\Http\Controllers\MonitoringController::class, 'getSystemOverview'])->name('system-overview');
    Route::get('/response-time', [App\Http\Controllers\MonitoringController::class, 'getResponseTimeStats'])->name('response-time');
    Route::get('/request-stats', [App\Http\Controllers\MonitoringController::class, 'getRequestStats'])->name('request-stats');
    Route::get('/error-rate', [App\Http\Controllers\MonitoringController::class, 'getErrorRateStats'])->name('error-rate');
    Route::get('/api-performance', [App\Http\Controllers\MonitoringController::class, 'getApiPerformance'])->name('api-performance');
    Route::get('/audit-logs', [App\Http\Controllers\MonitoringController::class, 'getAuditLogs'])->name('audit-logs');
    Route::get('/user-activity', [App\Http\Controllers\MonitoringController::class, 'getUserActivityStats'])->name('user-activity');
    Route::post('/clear-cache', [App\Http\Controllers\MonitoringController::class, 'clearCache'])->name('clear-cache');
    Route::get('/health-check', [App\Http\Controllers\MonitoringController::class, 'healthCheck'])->name('health-check');
});

// 响应式设计测试路由
Route::get('/responsive-test', function () {
    return view('responsive-test');
})->name('responsive-test');

// API配置的公共API路由（用于获取配置信息）
Route::prefix('api/v1/api-configurations')->name('api.api-configurations.')->middleware(['auth'])->group(function () {
    Route::get('/{slug}/config', [App\Http\Controllers\ApiConfigurationController::class, 'getClientConfig'])->name('client-config');
});

Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
Route::get('/search', [WebProductSearchController::class, 'index'])->name('search'); 