@extends('layouts.app')

@section('title', '任务分组管理')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="page-title">
                    <i class="fas fa-layer-group me-2"></i>
                    任务分组管理
                </h1>
                <div>
                    <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#createGroupModal">
                        <i class="fas fa-plus me-2"></i>创建分组
                    </button>
                    <button type="button" class="btn btn-info me-2" onclick="showDistributionAnalysis()">
                        <i class="fas fa-chart-pie me-2"></i>分布分析
                    </button>
                    <button type="button" class="btn btn-warning" onclick="showAutoGroupModal()">
                        <i class="fas fa-magic me-2"></i>智能分组
                    </button>
                </div>
            </div>

            <!-- 统计概览 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">总分组数</h5>
                                    <h3 id="totalGroups">--</h3>
                                </div>
                                <div>
                                    <i class="fas fa-layer-group fa-3x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">已分组任务</h5>
                                    <h3 id="groupedTasks">--</h3>
                                </div>
                                <div>
                                    <i class="fas fa-tasks fa-3x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">未分组任务</h5>
                                    <h3 id="ungroupedTasks">--</h3>
                                </div>
                                <div>
                                    <i class="fas fa-exclamation-triangle fa-3x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">分组率</h5>
                                    <h3 id="groupingRate">--%</h3>
                                </div>
                                <div>
                                    <i class="fas fa-percentage fa-3x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分组列表 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        分组列表
                        <small class="text-muted ms-2">(拖拽任务到分组以分配任务)</small>
                    </h5>
                </div>
                <div class="card-body">
                    <div id="groupsList" class="row">
                        <!-- 分组卡片将通过JavaScript动态加载 -->
                    </div>
                </div>
            </div>

            <!-- 未分组任务 -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-inbox me-2"></i>
                        未分组任务
                        <small class="text-muted ms-2">(拖拽到上方分组以分配)</small>
                    </h5>
                </div>
                <div class="card-body">
                    <div id="unassignedTasks" class="row">
                        <!-- 未分组任务将通过JavaScript动态加载 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建分组模态框 -->
<div class="modal fade" id="createGroupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建新分组</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createGroupForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="groupName" class="form-label">分组名称 *</label>
                        <input type="text" class="form-control" id="groupName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="groupDescription" class="form-label">分组描述</label>
                        <textarea class="form-control" id="groupDescription" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="groupColor" class="form-label">分组颜色</label>
                        <div class="d-flex align-items-center">
                            <input type="color" class="form-control form-control-color me-2" id="groupColor" name="color" value="#007bff">
                            <span class="color-preview">预览</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="sortOrder" class="form-label">排序顺序</label>
                        <input type="number" class="form-control" id="sortOrder" name="sort_order" value="0">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">创建分组</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 编辑分组模态框 -->
<div class="modal fade" id="editGroupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑分组</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editGroupForm">
                <input type="hidden" id="editGroupId" name="id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="editGroupName" class="form-label">分组名称 *</label>
                        <input type="text" class="form-control" id="editGroupName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="editGroupDescription" class="form-label">分组描述</label>
                        <textarea class="form-control" id="editGroupDescription" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="editGroupColor" class="form-label">分组颜色</label>
                        <div class="d-flex align-items-center">
                            <input type="color" class="form-control form-control-color me-2" id="editGroupColor" name="color">
                            <span class="edit-color-preview">预览</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="editSortOrder" class="form-label">排序顺序</label>
                        <input type="number" class="form-control" id="editSortOrder" name="sort_order">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存更改</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 智能分组模态框 -->
<div class="modal fade" id="autoGroupModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">智能分组设置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    系统将根据您设置的条件自动创建分组并分配任务
                </div>
                <div id="autoGroupCriteria">
                    <!-- 分组条件将在这里动态添加 -->
                </div>
                <button type="button" class="btn btn-outline-primary" onclick="addGroupCriterion()">
                    <i class="fas fa-plus me-2"></i>添加分组条件
                </button>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="executeAutoGrouping()">执行智能分组</button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('styles')
<style>
.group-card {
    border: 2px dashed #dee2e6;
    min-height: 200px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.group-card:hover {
    border-color: #007bff;
    background-color: rgba(0, 123, 255, 0.05);
}

.group-card.drag-over {
    border-color: #28a745;
    background-color: rgba(40, 167, 69, 0.1);
}

.task-item {
    cursor: move;
    transition: all 0.3s ease;
}

.task-item:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.task-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.color-preview, .edit-color-preview {
    padding: 4px 12px;
    border-radius: 4px;
    background: var(--bs-primary);
    color: white;
    font-size: 0.875em;
}

.group-header {
    border-left: 4px solid;
    padding-left: 12px;
}

.unassigned-area {
    min-height: 150px;
    border: 2px dashed #ffc107;
    border-radius: 8px;
    background-color: rgba(255, 193, 7, 0.05);
}

.criterion-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    background-color: #f8f9fa;
}
</style>
@endpush

@push('scripts')
<script src="{{ asset('js/task-groups.js') }}"></script>
@endpush 