/**
 * 竞争对手搜索页面JavaScript
 */
class CompetitorSearch {
    constructor() {
        this.searchResults = [];
        this.currentSearchParams = {};
        this.compareList = [];
        this.selectedProducts = new Set();
        this.watchlist = JSON.parse(localStorage.getItem('competitorWatchlist') || '[]');
        this.dataTable = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.setupAutoComplete();
    }

    bindEvents() {
        // 搜索表单提交
        document.getElementById('searchForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.performSearch();
        });

        // 清空表单
        document.getElementById('clearForm').addEventListener('click', () => {
            this.clearForm();
        });

        // 保存搜索
        document.getElementById('saveSearch').addEventListener('click', () => {
            this.saveSearch();
        });

        // 导出结果
        document.getElementById('exportResults').addEventListener('click', () => {
            this.showExportModal();
        });

        // 确认导出
        document.getElementById('confirmExport').addEventListener('click', () => {
            this.exportResults();
        });

        // 关键词输入自动完成
        document.getElementById('keywords').addEventListener('input', 
            this.debounce((e) => this.handleKeywordInput(e), 300)
        );

        // 全选/取消全选
        document.getElementById('selectAll').addEventListener('change', (e) => {
            this.toggleSelectAll(e.target.checked);
        });

        // 比较选中产品
        document.getElementById('compareSelected').addEventListener('click', () => {
            this.compareSelectedProducts();
        });

        // 清空选择
        document.getElementById('clearSelection').addEventListener('click', () => {
            this.clearSelection();
        });
    }

    // 显示分析报告
    showAnalysisReport() {
        if (this.searchResults.length === 0) {
            alert('请先进行搜索以获取分析数据');
            return;
        }

        // 显示分析报告模态框
        const modal = new bootstrap.Modal(document.getElementById('analysisReportModal'));
        modal.show();

        // 初始化图表
        setTimeout(() => {
            this.initializeAnalysisCharts();
        }, 500);
    }

    // 初始化分析图表
    initializeAnalysisCharts() {
        // 价格分布图
        const priceCtx = document.getElementById('priceDistributionChart');
        if (priceCtx) {
            new Chart(priceCtx.getContext('2d'), {
                type: 'bar',
                data: {
                    labels: ['0-100', '100-500', '500-1000', '1000-2000', '2000+'],
                    datasets: [{
                        label: '商品数量',
                        data: this.generatePriceDistributionData(),
                        backgroundColor: 'rgba(78, 115, 223, 0.8)'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: '价格分布'
                        }
                    }
                }
            });
        }

        // 平台分布图
        const platformCtx = document.getElementById('platformDistributionChart');
        if (platformCtx) {
            new Chart(platformCtx.getContext('2d'), {
                type: 'doughnut',
                data: {
                    labels: ['淘宝', '天猫', '京东', '拼多多'],
                    datasets: [{
                        data: this.generatePlatformDistributionData(),
                        backgroundColor: [
                            '#f39c12',
                            '#e74c3c',
                            '#3498db',
                            '#2ecc71'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: '平台分布'
                        }
                    }
                }
            });
        }

        // 价格趋势图
        const trendCtx = document.getElementById('priceTrendChart');
        if (trendCtx) {
            new Chart(trendCtx.getContext('2d'), {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '平均价格',
                        data: [1250, 1180, 1320, 1280, 1150, 1200],
                        borderColor: '#4e73df',
                        backgroundColor: 'rgba(78, 115, 223, 0.1)',
                        tension: 0.3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: '价格趋势'
                        }
                    }
                }
            });
        }

        // 填充竞争力分析表格
        this.fillCompetitiveAnalysisTable();
    }

    // 生成价格分布数据
    generatePriceDistributionData() {
        const ranges = [
            { min: 0, max: 100 },
            { min: 100, max: 500 },
            { min: 500, max: 1000 },
            { min: 1000, max: 2000 },
            { min: 2000, max: Infinity }
        ];

        return ranges.map(range => {
            return this.searchResults.filter(product => {
                const price = parseFloat(product.price);
                return price >= range.min && price < range.max;
            }).length;
        });
    }

    // 生成平台分布数据
    generatePlatformDistributionData() {
        const platforms = {};
        this.searchResults.forEach(product => {
            platforms[product.platform] = (platforms[product.platform] || 0) + 1;
        });

        return ['淘宝', '天猫', '京东', '拼多多'].map(platform => platforms[platform] || 0);
    }

    // 填充竞争力分析表格
    fillCompetitiveAnalysisTable() {
        const tbody = document.getElementById('competitiveAnalysisTable');
        if (!tbody) return;

        const analysisData = [
            {
                metric: '平均价格',
                ourProduct: '¥1,250',
                competitorAvg: '¥1,180',
                advantage: '劣势 (-5.9%)',
                suggestion: '考虑适当降价或增加价值'
            },
            {
                metric: '销量',
                ourProduct: '2,500/月',
                competitorAvg: '1,800/月',
                advantage: '优势 (+38.9%)',
                suggestion: '保持当前销售策略'
            },
            {
                metric: '评分',
                ourProduct: '4.6',
                competitorAvg: '4.3',
                advantage: '优势 (****%)',
                suggestion: '继续提升产品质量'
            },
            {
                metric: '店铺信誉',
                ourProduct: '皇冠',
                competitorAvg: '钻石',
                advantage: '优势',
                suggestion: '维持良好服务水平'
            }
        ];

        tbody.innerHTML = analysisData.map(item => `
            <tr>
                <td><strong>${item.metric}</strong></td>
                <td>${item.ourProduct}</td>
                <td>${item.competitorAvg}</td>
                <td>
                    <span class="badge ${item.advantage.includes('优势') ? 'bg-success' : 'bg-warning'}">
                        ${item.advantage}
                    </span>
                </td>
                <td><small>${item.suggestion}</small></td>
            </tr>
        `).join('');
    }

    // 导出分析报告
    exportAnalysisReport() {
        console.log('导出分析报告');
        alert('分析报告导出功能开发中...');
    }

    setupAutoComplete() {
        // 设置关键词自动完成
        const keywordsInput = document.getElementById('keywords');
        let suggestionsList = null;

        keywordsInput.addEventListener('focus', () => {
            if (!suggestionsList) {
                suggestionsList = this.createSuggestionsList(keywordsInput);
            }
        });

        keywordsInput.addEventListener('blur', () => {
            setTimeout(() => {
                if (suggestionsList) {
                    suggestionsList.style.display = 'none';
                }
            }, 200);
        });
    }

    createSuggestionsList(input) {
        const list = document.createElement('div');
        list.className = 'suggestions-list position-absolute bg-white border rounded shadow-sm';
        list.style.cssText = `
            top: 100%;
            left: 0;
            right: 0;
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
            display: none;
        `;
        
        input.parentNode.style.position = 'relative';
        input.parentNode.appendChild(list);
        
        return list;
    }

    async handleKeywordInput(e) {
        const query = e.target.value.trim();
        if (query.length < 2) {
            this.hideSuggestions(e.target);
            return;
        }

        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

            const response = await fetch(`/api/v1/competitors/suggestions?query=${encodeURIComponent(query)}`, {
                signal: controller.signal,
                headers: {
                    'Accept': 'application/json'
                }
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();
            
            if (data.success && data.suggestions.length > 0) {
                this.showSuggestions(e.target, data.suggestions);
            } else {
                this.hideSuggestions(e.target);
            }
        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('获取搜索建议失败:', error);
            }
            this.hideSuggestions(e.target);
        }
    }

    showSuggestions(input, suggestions) {
        const list = input.parentNode.querySelector('.suggestions-list');
        if (!list) return;

        list.innerHTML = '';
        
        suggestions.forEach(suggestion => {
            const item = document.createElement('div');
            item.className = 'suggestion-item p-2 cursor-pointer';
            item.style.cssText = 'cursor: pointer; border-bottom: 1px solid #eee;';
            
            item.innerHTML = `
                <div class="fw-bold">${this.escapeHtml(suggestion.title)}</div>
                <small class="text-muted">${this.escapeHtml(suggestion.category)} - ${this.escapeHtml(suggestion.shop)}</small>
            `;
            
            item.addEventListener('click', () => {
                input.value = suggestion.title;
                list.style.display = 'none';
            });
            
            item.addEventListener('mouseenter', () => {
                item.style.backgroundColor = '#f8f9fa';
            });
            
            item.addEventListener('mouseleave', () => {
                item.style.backgroundColor = 'white';
            });
            
            list.appendChild(item);
        });
        
        list.style.display = 'block';
    }

    hideSuggestions(input) {
        const list = input.parentNode.querySelector('.suggestions-list');
        if (list) {
            list.style.display = 'none';
        }
    }

    async performSearch() {
        const searchParams = this.getSearchParams();
        
        if (!this.validateSearchParams(searchParams)) {
            return;
        }

        this.showLoading(true);
        this.hideResults();
        this.clearErrorState();

        let retryCount = 0;
        const maxRetries = 3;
        const retryDelay = 1000; // 1秒

        while (retryCount <= maxRetries) {
            try {
                const response = await this.makeSearchRequest(searchParams);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.success) {
                    this.searchResults = data.data;
                    this.currentSearchParams = searchParams;
                    this.displayResults(data);
                    this.showSuccess(`搜索完成，找到 ${data.data.length} 个相关产品`);
                    break;
                } else {
                    this.handleSearchError(data, retryCount, maxRetries);
                    if (retryCount < maxRetries && this.shouldRetry(data.error_code)) {
                        retryCount++;
                        await this.delay(retryDelay * retryCount);
                        continue;
                    }
                    break;
                }
            } catch (error) {
                console.error('搜索请求失败:', error);
                
                if (retryCount < maxRetries && this.isRetriableError(error)) {
                    retryCount++;
                    this.showInfo(`搜索失败，正在重试 (${retryCount}/${maxRetries})...`);
                    await this.delay(retryDelay * retryCount);
                    continue;
                } else {
                    this.handleNetworkError(error);
                    break;
                }
            }
        }

        this.showLoading(false);
    }

    async makeSearchRequest(searchParams) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

        try {
            const response = await fetch('/api/v1/competitors/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json'
                },
                body: JSON.stringify(searchParams),
                signal: controller.signal
            });

            clearTimeout(timeoutId);
            return response;
        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    }

    handleSearchError(data, retryCount, maxRetries) {
        const errorCode = data.error_code || 'UNKNOWN_ERROR';
        const errorMessage = data.error || '搜索失败';

        switch (errorCode) {
            case 'VALIDATION_ERROR':
                this.showError('输入参数有误，请检查搜索条件');
                this.highlightValidationErrors(data.errors);
                break;
            case 'NO_SEARCH_CRITERIA':
                this.showError('请输入搜索关键词或选择分类');
                document.getElementById('keywords').focus();
                break;
            case 'TOO_MANY_KEYWORDS':
                this.showError('关键词数量过多，请减少关键词数量');
                break;
            case 'TOO_MANY_CATEGORIES':
                this.showError('分类数量过多，请减少分类数量');
                break;
            case 'DATABASE_ERROR':
                if (retryCount < maxRetries) {
                    this.showInfo('数据库连接异常，正在重试...');
                } else {
                    this.showError('数据库服务暂时不可用，请稍后重试');
                }
                break;
            case 'SEARCH_ENGINE_ERROR':
                this.showError('搜索引擎服务异常，请稍后重试');
                break;
            case 'RATE_LIMIT_EXCEEDED':
                const retryAfter = data.retry_after || 60;
                this.showError(`请求过于频繁，请 ${retryAfter} 秒后重试`);
                this.startRetryCountdown(retryAfter);
                break;
            default:
                this.showError(errorMessage);
        }
    }

    handleNetworkError(error) {
        if (error.name === 'AbortError') {
            this.showError('搜索超时，请检查网络连接或稍后重试');
        } else if (error.message.includes('Failed to fetch')) {
            this.showError('网络连接失败，请检查网络设置');
        } else if (error.message.includes('NetworkError')) {
            this.showError('网络错误，请稍后重试');
        } else {
            this.showError('搜索请求失败：' + error.message);
        }
    }

    isRetriableError(error) {
        // 可重试的错误类型
        const retriableErrors = [
            'NetworkError',
            'TimeoutError',
            'Failed to fetch'
        ];
        
        return retriableErrors.some(errType => 
            error.message.includes(errType) || error.name === errType
        );
    }

    shouldRetry(errorCode) {
        // 可重试的错误码
        const retriableErrorCodes = [
            'DATABASE_ERROR',
            'SEARCH_ENGINE_ERROR',
            'SERVICE_UNAVAILABLE',
            'NETWORK_ERROR'
        ];
        
        return retriableErrorCodes.includes(errorCode);
    }

    highlightValidationErrors(errors) {
        // 清除之前的错误样式
        document.querySelectorAll('.is-invalid').forEach(el => {
            el.classList.remove('is-invalid');
        });
        
        document.querySelectorAll('.invalid-feedback').forEach(el => {
            el.remove();
        });

        // 高亮显示有错误的字段
        if (errors) {
            Object.keys(errors).forEach(field => {
                const element = document.getElementById(field) || 
                              document.querySelector(`[name="${field}"]`);
                
                if (element) {
                    element.classList.add('is-invalid');
                    
                    const feedback = document.createElement('div');
                    feedback.className = 'invalid-feedback';
                    feedback.textContent = errors[field][0];
                    element.parentNode.appendChild(feedback);
                }
            });
        }
    }

    startRetryCountdown(seconds) {
        const searchButton = document.querySelector('button[type="submit"]');
        const originalText = searchButton.textContent;
        
        searchButton.disabled = true;
        
        const countdown = setInterval(() => {
            searchButton.innerHTML = `<i class="fas fa-clock"></i> ${seconds}秒后可重试`;
            seconds--;
            
            if (seconds <= 0) {
                clearInterval(countdown);
                searchButton.disabled = false;
                searchButton.innerHTML = '<i class="fas fa-search"></i> 搜索';
            }
        }, 1000);
    }

    clearErrorState() {
        // 清除错误样式
        document.querySelectorAll('.is-invalid').forEach(el => {
            el.classList.remove('is-invalid');
        });
        
        document.querySelectorAll('.invalid-feedback').forEach(el => {
            el.remove();
        });

        // 隐藏错误提示
        this.hideAlert();
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    getSearchParams() {
        const keywords = document.getElementById('keywords').value.trim();
        const excludeWords = document.getElementById('excludeWords').value.trim();
        const categories = document.getElementById('categories').value.trim();
        
        const params = {
            keywords: keywords ? keywords.split(/\s+/) : [],
            exclude_words: excludeWords ? excludeWords.split(/\s+/) : [],
            categories: categories ? categories.split(',').map(c => c.trim()) : [],
            operator: document.getElementById('operator').value,
            limit: parseInt(document.getElementById('limit').value),
            filters: {}
        };

        // 高级筛选
        const platform = document.getElementById('platform').value;
        if (platform) params.filters.platform = platform;

        const minPrice = parseFloat(document.getElementById('minPrice').value);
        if (!isNaN(minPrice) && minPrice > 0) params.filters.min_price = minPrice;

        const maxPrice = parseFloat(document.getElementById('maxPrice').value);
        if (!isNaN(maxPrice) && maxPrice > 0) params.filters.max_price = maxPrice;

        const minSales = parseInt(document.getElementById('minSales').value);
        if (!isNaN(minSales) && minSales > 0) params.filters.min_sales = minSales;

        const minRating = parseFloat(document.getElementById('minRating').value);
        if (!isNaN(minRating) && minRating > 0) params.filters.min_rating = minRating;

        return params;
    }

    validateSearchParams(params) {
        if (params.keywords.length === 0 && params.categories.length === 0) {
            this.showError('请输入关键词或分类路径');
            return false;
        }
        return true;
    }

    displayResults(data) {
        const results = data.data || [];
        const meta = data.meta || {};
        
        // 显示统计信息
        this.displayStats(results, meta);
        
        // 显示结果表格
        this.displayResultsTable(results);
        
        // 显示结果卡片
        this.showResults();
    }

    displayStats(results, meta) {
        const totalResults = results.length;
        const totalPrice = results.reduce((sum, product) => sum + (product.min_price || 0), 0);
        const avgPrice = totalResults > 0 ? totalPrice / totalResults : 0;
        const totalSales = results.reduce((sum, product) => sum + (product.total_sales || 0), 0);
        const totalRating = results.reduce((sum, product) => sum + (product.rating || 0), 0);
        const avgRating = totalResults > 0 ? totalRating / totalResults : 0;

        document.getElementById('totalResults').textContent = totalResults;
        document.getElementById('avgPrice').textContent = '¥' + avgPrice.toFixed(2);
        document.getElementById('totalSales').textContent = totalSales.toLocaleString();
        document.getElementById('avgRating').textContent = avgRating.toFixed(1);
    }

    displayResultsTable(results) {
        // 销毁现有的DataTable实例
        if (this.dataTable) {
            this.dataTable.destroy();
            this.dataTable = null;
        }

        const tbody = document.getElementById('resultsTableBody');
        tbody.innerHTML = '';

        if (results.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="10" class="text-center py-4">
                        <i class="fas fa-search fa-2x text-muted mb-2"></i>
                        <p class="text-muted">未找到匹配的竞争对手产品</p>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="competitorSearch.clearForm()">
                            <i class="fas fa-redo"></i> 重新搜索
                        </button>
                    </td>
                </tr>
            `;
            return;
        }

        results.forEach((product, index) => {
            const row = document.createElement('tr');
            row.className = 'product-row';
            row.dataset.productId = product.id;
            
            // 添加相关性评分显示
            const relevanceScore = product.relevance_score || 0;
            const scoreColor = relevanceScore >= 80 ? 'success' : relevanceScore >= 60 ? 'warning' : 'secondary';
            
            row.innerHTML = `
                <td class="text-center">
                    <input type="checkbox" class="form-check-input product-checkbox" 
                           value="${product.id}" onchange="competitorSearch.handleProductSelection(this)">
                </td>
                <td class="text-center">
                    <small class="text-muted">#${index + 1}</small>
                    <br>
                    <span class="badge bg-${scoreColor} relevance-score" title="相关性评分">
                        ${relevanceScore.toFixed(1)}
                    </span>
                </td>
                <td>
                    <div class="product-title" title="${this.escapeHtml(product.title)}">
                        <a href="${this.escapeHtml(product.url || '#')}" target="_blank" class="text-decoration-none">
                            ${this.escapeHtml(product.title)}
                        </a>
                        ${product.is_promoted ? '<span class="badge bg-warning ms-1">推广</span>' : ''}
                    </div>
                    ${product.description ? `<small class="text-muted d-block mt-1">${this.escapeHtml(product.description.substring(0, 100))}...</small>` : ''}
                </td>
                <td>
                    <small class="category-path" title="${this.escapeHtml(product.category_path || '未分类')}">
                        ${this.formatCategoryPath(product.category_path || '未分类')}
                    </small>
                </td>
                <td>
                    <span class="badge bg-info platform-badge">
                        ${this.getPlatformName(product.source_platform)}
                    </span>
                </td>
                <td>
                    <div class="shop-info">
                        <small class="d-block">${this.escapeHtml(product.shop_name || '未知店铺')}</small>
                        ${product.shop_level ? `<span class="badge bg-light text-dark">${product.shop_level}</span>` : ''}
                    </div>
                </td>
                <td>
                    <div class="price-info">
                        <span class="price-highlight">¥${(product.min_price || 0).toFixed(2)}</span>
                        ${product.max_price && product.max_price !== product.min_price ? 
                            `<br><small class="text-muted">~¥${product.max_price.toFixed(2)}</small>` : ''}
                        ${product.discount ? `<br><span class="badge bg-danger">-${product.discount}%</span>` : ''}
                    </div>
                </td>
                <td class="text-center">
                    <strong>${(product.total_sales || 0).toLocaleString()}</strong>
                    ${product.monthly_sales ? `<br><small class="text-muted">月销: ${product.monthly_sales}</small>` : ''}
                </td>
                <td class="text-center">
                    <div class="rating-info">
                        <span class="rating-stars">
                            ${this.generateStarRating(product.rating || 0)}
                        </span>
                        <br>
                        <small class="text-muted">(${(product.rating || 0).toFixed(1)})</small>
                        ${product.review_count ? `<br><small class="text-muted">${product.review_count}评价</small>` : ''}
                    </div>
                </td>
                <td>
                    <div class="btn-group-vertical btn-group-sm w-100">
                        <button type="button" class="btn btn-outline-primary btn-sm mb-1" 
                                onclick="competitorSearch.viewProductDetails(${product.id})" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm mb-1" 
                                onclick="competitorSearch.findSimilar(${product.id})" title="找相似">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm mb-1" 
                                onclick="competitorSearch.addToCompare(${product.id})" title="添加对比">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button type="button" class="btn btn-outline-warning btn-sm" 
                                onclick="competitorSearch.addToWatchlist(${product.id})" title="关注">
                            <i class="fas fa-star"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
        
        // 初始化DataTables
        this.initializeDataTable();
    }

    formatCategoryPath(categoryPath) {
        if (!categoryPath || categoryPath === '未分类') {
            return '未分类';
        }
        
        const segments = categoryPath.split('/');
        if (segments.length <= 2) {
            return categoryPath;
        }
        
        // 显示前两级和最后一级
        return segments[0] + '/.../' + segments[segments.length - 1];
    }

    enableTableSorting() {
        const table = document.getElementById('resultsTable');
        const headers = table.querySelectorAll('th[data-sort]');
        
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => {
                const sortKey = header.dataset.sort;
                this.sortTable(sortKey);
            });
        });
    }

    sortTable(sortKey) {
        const tbody = document.getElementById('resultsTableBody');
        const rows = Array.from(tbody.querySelectorAll('tr.product-row'));
        
        // 获取当前排序状态
        const currentOrder = tbody.dataset.sortOrder || 'asc';
        const newOrder = currentOrder === 'asc' ? 'desc' : 'asc';
        
        rows.sort((a, b) => {
            const productA = this.searchResults.find(p => p.id == a.dataset.productId);
            const productB = this.searchResults.find(p => p.id == b.dataset.productId);
            
            let valueA, valueB;
            
            switch (sortKey) {
                case 'price':
                    valueA = productA.min_price || 0;
                    valueB = productB.min_price || 0;
                    break;
                case 'sales':
                    valueA = productA.total_sales || 0;
                    valueB = productB.total_sales || 0;
                    break;
                case 'rating':
                    valueA = productA.rating || 0;
                    valueB = productB.rating || 0;
                    break;
                case 'relevance':
                    valueA = productA.relevance_score || 0;
                    valueB = productB.relevance_score || 0;
                    break;
                default:
                    return 0;
            }
            
            if (newOrder === 'asc') {
                return valueA - valueB;
            } else {
                return valueB - valueA;
            }
        });
        
        // 重新排列表格行
        tbody.innerHTML = '';
        rows.forEach(row => tbody.appendChild(row));
        
        // 更新排序状态
        tbody.dataset.sortOrder = newOrder;
        tbody.dataset.sortKey = sortKey;
        
        // 更新表头图标
        this.updateSortIcons(sortKey, newOrder);
    }

    updateSortIcons(activeKey, order) {
        const headers = document.querySelectorAll('th[data-sort]');
        headers.forEach(header => {
            const icon = header.querySelector('.sort-icon');
            if (icon) icon.remove();
            
            if (header.dataset.sort === activeKey) {
                const iconClass = order === 'asc' ? 'fa-sort-up' : 'fa-sort-down';
                header.innerHTML += ` <i class="fas ${iconClass} sort-icon"></i>`;
            }
        });
    }

    // 处理产品选择
    handleProductSelection(checkbox) {
        const productId = parseInt(checkbox.value);
        
        if (checkbox.checked) {
            // 限制最多选择5个产品进行比较
            if (this.selectedProducts.size >= 5) {
                checkbox.checked = false;
                this.showToast('最多只能选择5个产品进行比较', 'warning');
                return;
            }
            this.selectedProducts.add(productId);
        } else {
            this.selectedProducts.delete(productId);
        }
        
        this.updateCompareButton();
    }

    // 更新比较按钮状态
    updateCompareButton() {
        const compareBtn = document.getElementById('compareSelectedBtn');
        const clearBtn = document.getElementById('clearSelectionBtn');
        const selectedCount = this.selectedProducts.size;
        
        if (compareBtn) {
            compareBtn.disabled = selectedCount < 2;
            compareBtn.querySelector('.selected-count').textContent = selectedCount;
        }
        
        if (clearBtn) {
            clearBtn.disabled = selectedCount === 0;
        }
    }

    // 全选/取消全选
    selectAllProducts() {
        const checkboxes = document.querySelectorAll('.product-checkbox');
        const selectAllCheckbox = document.getElementById('selectAllProducts');
        const isChecked = selectAllCheckbox.checked;
        
        this.selectedProducts.clear();
        
        if (isChecked) {
            checkboxes.forEach((checkbox, index) => {
                if (index < 5) { // 最多选择5个
                    checkbox.checked = true;
                    this.selectedProducts.add(parseInt(checkbox.value));
                } else {
                    checkbox.checked = false;
                }
            });
            
            if (checkboxes.length > 5) {
                this.showToast('已选择前5个产品（最多比较5个产品）', 'info');
            }
        } else {
            checkboxes.forEach(checkbox => checkbox.checked = false);
        }
        
        this.updateCompareButton();
    }

    // 清空选择
    clearSelection() {
        this.selectedProducts.clear();
        document.querySelectorAll('.product-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });
        document.getElementById('selectAllProducts').checked = false;
        this.updateCompareButton();
    }

    // 比较选中的产品
    compareSelectedProducts() {
        if (this.selectedProducts.size < 2) {
            this.showToast('请至少选择2个产品进行比较', 'warning');
            return;
        }
        
        const productIds = Array.from(this.selectedProducts);
        const selectedProductsData = this.getSelectedProductsData(productIds);
        
        if (selectedProductsData.length === 0) {
            this.showToast('无法获取选中产品的数据', 'error');
            return;
        }
        
        this.showComparisonModal(selectedProductsData);
        this.showToast(`正在比较${selectedProductsData.length}个产品...`, 'info');
    }

    // 获取选中产品的详细数据
    getSelectedProductsData(productIds) {
        const selectedData = [];
        
        productIds.forEach(id => {
            // 从搜索结果中查找产品数据
            const product = this.searchResults.find(p => p.id === id);
            if (product) {
                selectedData.push(product);
            } else {
                // 如果搜索结果中没有，尝试从表格行中获取数据
                const tableProduct = this.extractProductDataFromTableRow(id);
                if (tableProduct) {
                    selectedData.push(tableProduct);
                }
            }
        });
        
        return selectedData;
    }

    // 从表格行中提取产品数据
    extractProductDataFromTableRow(productId) {
        const row = document.querySelector(`tr[data-product-id="${productId}"]`);
        if (!row) return null;
        
        const cells = row.querySelectorAll('td');
        if (cells.length < 8) return null;
        
        try {
            // 提取标题和链接
            const titleElement = cells[2].querySelector('.product-title a');
            const title = titleElement ? titleElement.textContent.trim() : '未知产品';
            const url = titleElement ? titleElement.href : '#';
            
            // 提取分类
            const categoryElement = cells[3].querySelector('.category-path');
            const category_path = categoryElement ? categoryElement.textContent.trim() : '未分类';
            
            // 提取平台
            const platformElement = cells[4].querySelector('.platform-badge');
            const source_platform = platformElement ? platformElement.textContent.trim() : '未知';
            
            // 提取店铺信息
            const shopElement = cells[5].querySelector('.shop-info small');
            const shop_name = shopElement ? shopElement.textContent.trim() : '未知店铺';
            
            // 提取价格信息
            const priceElement = cells[6].querySelector('.price-highlight');
            const priceText = priceElement ? priceElement.textContent.trim() : '¥0.00';
            const priceMatch = priceText.match(/¥([\d,.]+)/);
            const min_price = priceMatch ? parseFloat(priceMatch[1].replace(/,/g, '')) : 0;
            
            // 提取销量
            const salesElement = cells[7].querySelector('strong');
            const salesText = salesElement ? salesElement.textContent.trim() : '0';
            const total_sales = parseInt(salesText.replace(/,/g, '')) || 0;
            
            // 提取评分
            const ratingElement = cells[8].querySelector('.text-muted');
            const ratingText = ratingElement ? ratingElement.textContent.trim() : '(0.0)';
            const ratingMatch = ratingText.match(/\(([\d.]+)\)/);
            const rating = ratingMatch ? parseFloat(ratingMatch[1]) : 0;
            
            return {
                id: parseInt(productId),
                title,
                url,
                category_path,
                source_platform,
                shop_name,
                min_price,
                total_sales,
                rating,
                // 默认值
                description: '',
                shop_level: '',
                max_price: min_price,
                discount: 0,
                monthly_sales: 0,
                review_count: 0,
                is_promoted: false
            };
        } catch (error) {
            console.error('Error extracting product data from table row:', error);
            return null;
        }
    }

    // 初始化DataTables
    initializeDataTable() {
        const table = document.getElementById('resultsTable');
        if (!table) return;

        this.dataTable = $(table).DataTable({
            // 基本配置
            paging: true,
            searching: true,
            ordering: true,
            info: true,
            responsive: true,
            
            // 分页配置
            pageLength: 25,
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "全部"]],
            
            // 语言配置
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.13.7/i18n/zh.json'
            },
            
            // 列配置
            columnDefs: [
                { 
                    targets: 0, // 选择框列
                    orderable: false,
                    searchable: false,
                    className: 'text-center'
                },
                { 
                    targets: 1, // 序号列
                    orderable: false,
                    searchable: false,
                    className: 'text-center'
                },
                { 
                    targets: -1, // 操作列
                    orderable: false,
                    searchable: false,
                    className: 'text-center'
                },
                {
                    targets: 6, // 价格列
                    type: 'num',
                    render: function(data, type, row) {
                        if (type === 'sort' || type === 'type') {
                            // 提取数字进行排序
                            const match = data.match(/¥([\d,.]+)/);
                            return match ? parseFloat(match[1].replace(/,/g, '')) : 0;
                        }
                        return data;
                    }
                },
                {
                    targets: 7, // 销量列
                    type: 'num',
                    render: function(data, type, row) {
                        if (type === 'sort' || type === 'type') {
                            // 提取数字进行排序
                            const match = data.match(/<strong>([\d,]+)<\/strong>/);
                            return match ? parseInt(match[1].replace(/,/g, '')) : 0;
                        }
                        return data;
                    }
                }
            ],
            
            // 默认排序
            order: [[1, 'asc']], // 按序号排序
            
            // DOM配置
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                 '<"row"<"col-sm-12"tr>>' +
                 '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
            
            // 初始化完成回调
            initComplete: function() {
                // 添加自定义搜索过滤器
                competitorSearch.addCustomFilters();
            },
            
            // 绘制完成回调
            drawCallback: function() {
                // 重新绑定工具提示
                $('[title]').tooltip();
                
                // 更新选择状态
                competitorSearch.updateSelectionState();
            }
        });
    }

    // 添加自定义过滤器
    addCustomFilters() {
        // 可以在这里添加平台、价格范围等过滤器
        // TODO: 实现自定义过滤器
    }

    // 更新选择状态
    updateSelectionState() {
        // 重新应用选择状态
        document.querySelectorAll('.product-checkbox').forEach(checkbox => {
            const productId = parseInt(checkbox.value);
            checkbox.checked = this.selectedProducts.has(productId);
        });
        
        this.updateCompareButton();
    }

    // 显示Toast消息
    showToast(message, type = 'info') {
        // 创建toast元素
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'info'} border-0`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');
        
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        // 添加到页面
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '9999';
            document.body.appendChild(toastContainer);
        }
        
        toastContainer.appendChild(toast);
        
        // 显示toast
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // 自动移除
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }

    getPlatformName(platform) {
        const platformNames = {
            'taobao': '淘宝',
            'tmall': '天猫',
            'jd': '京东',
            'pdd': '拼多多'
        };
        return platformNames[platform] || platform || '未知';
    }

    generateStarRating(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
        
        let stars = '';
        for (let i = 0; i < fullStars; i++) {
            stars += '<i class="fas fa-star"></i>';
        }
        if (hasHalfStar) {
            stars += '<i class="fas fa-star-half-alt"></i>';
        }
        for (let i = 0; i < emptyStars; i++) {
            stars += '<i class="far fa-star"></i>';
        }
        
        return stars;
    }

    async viewProductDetails(productId) {
        // 这里可以实现产品详情查看功能
        console.log('查看产品详情:', productId);
        this.showInfo('产品详情功能待实现');
    }

    async findSimilar(productId) {
        this.showLoading(true);
        
        try {
            const response = await fetch(`/api/v1/competitors/find-similar/${productId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    limit: 50,
                    operator: 'OR'
                })
            });

            const data = await response.json();
            
            if (data.success) {
                this.searchResults = data.data;
                this.displayResults(data);
                this.showSuccess('找到 ' + data.data.length + ' 个相似竞争对手产品');
            } else {
                this.showError('查找相似产品失败: ' + (data.error || '未知错误'));
            }
        } catch (error) {
            console.error('查找相似产品失败:', error);
            this.showError('查找相似产品失败，请检查网络连接');
        } finally {
            this.showLoading(false);
        }
    }

    addToCompare(productId) {
        const product = this.searchResults.find(p => p.id === productId);
        if (!product) {
            this.showError('产品不存在');
            return;
        }

        if (this.compareList.find(p => p.id === productId)) {
            this.showInfo('产品已在对比列表中');
            return;
        }

        if (this.compareList.length >= 5) {
            this.showError('对比列表最多只能添加5个产品');
            return;
        }

        this.compareList.push(product);
        this.updateCompareButton(productId, true);
        this.showSuccess(`已添加 "${product.title}" 到对比列表`);
        this.updateCompareCounter();
    }

    removeFromCompare(productId) {
        const index = this.compareList.findIndex(p => p.id === productId);
        if (index > -1) {
            const product = this.compareList[index];
            this.compareList.splice(index, 1);
            this.updateCompareButton(productId, false);
            this.showSuccess(`已从对比列表移除 "${product.title}"`);
            this.updateCompareCounter();
        }
    }

    updateCompareButton(productId, added) {
        const button = document.querySelector(`button[onclick*="addToCompare(${productId})"]`);
        if (button) {
            if (added) {
                button.className = 'btn btn-info btn-sm mb-1';
                button.innerHTML = '<i class="fas fa-check"></i>';
                button.title = '已添加对比';
                button.onclick = () => this.removeFromCompare(productId);
            } else {
                button.className = 'btn btn-outline-info btn-sm mb-1';
                button.innerHTML = '<i class="fas fa-plus"></i>';
                button.title = '添加对比';
                button.onclick = () => this.addToCompare(productId);
            }
        }
    }

    updateCompareCounter() {
        const counter = document.getElementById('compareCounter');
        if (counter) {
            counter.textContent = this.compareList.length;
            counter.style.display = this.compareList.length > 0 ? 'inline' : 'none';
        }
    }

    // 显示新的产品比较模态框
    showComparisonModal(products) {
        const modal = this.createProductComparisonModal(products);
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();
        
        // 模态框显示后初始化Chart.js图表
        modal.addEventListener('shown.bs.modal', () => {
            this.initializeComparisonCharts(products);
        });
    }

    showCompareModal() {
        if (this.compareList.length === 0) {
            this.showError('对比列表为空');
            return;
        }

        // 创建对比模态框
        const modal = this.createCompareModal();
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();
    }

    // 创建新的产品比较模态框
    createProductComparisonModal(products) {
        const existingModal = document.getElementById('productComparisonModal');
        if (existingModal) {
            existingModal.remove();
        }

        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'productComparisonModal';
        modal.innerHTML = `
            <div class="modal-dialog modal-fullscreen">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-balance-scale me-2"></i>产品比较分析
                            <small class="ms-2">(${products.length}个产品)</small>
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body p-0">
                        ${this.generateProductComparisonContent(products)}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>关闭
                        </button>
                        <button type="button" class="btn btn-primary" onclick="competitorSearch.exportComparison()">
                            <i class="fas fa-download me-1"></i>导出比较结果
                        </button>
                        <button type="button" class="btn btn-warning" onclick="competitorSearch.clearSelection(); bootstrap.Modal.getInstance(document.getElementById('productComparisonModal')).hide();">
                            <i class="fas fa-trash me-1"></i>清空选择
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        return modal;
    }

    createCompareModal() {
        const existingModal = document.getElementById('compareModal');
        if (existingModal) {
            existingModal.remove();
        }

        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'compareModal';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">产品对比</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${this.generateCompareTable()}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-danger" onclick="competitorSearch.clearCompareList()">清空对比</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        return modal;
    }

    // 生成产品比较内容
    generateProductComparisonContent(products) {
        return `
            <div class="container-fluid h-100">
                <div class="row h-100">
                    <!-- 左侧比较表格 -->
                    <div class="col-lg-8 border-end">
                        <div class="p-3">
                            <h6 class="text-muted mb-3">
                                <i class="fas fa-table me-2"></i>详细比较
                            </h6>
                            ${this.generateSideBySideComparisonTable(products)}
                        </div>
                    </div>
                    
                    <!-- 右侧图表分析 -->
                    <div class="col-lg-4">
                        <div class="p-3">
                            <h6 class="text-muted mb-3">
                                <i class="fas fa-chart-bar me-2"></i>可视化分析
                            </h6>
                            
                            <!-- 价格对比图 -->
                            <div class="mb-4">
                                <h6 class="h6">价格对比</h6>
                                <canvas id="priceComparisonChart" width="400" height="200"></canvas>
                            </div>
                            
                            <!-- 销量对比图 -->
                            <div class="mb-4">
                                <h6 class="h6">销量对比</h6>
                                <canvas id="salesComparisonChart" width="400" height="200"></canvas>
                            </div>
                            
                            <!-- 评分对比图 -->
                            <div class="mb-4">
                                <h6 class="h6">评分对比</h6>
                                <canvas id="ratingComparisonChart" width="400" height="200"></canvas>
                            </div>
                            
                            <!-- 综合评价 -->
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-star me-2"></i>综合评价
                                    </h6>
                                </div>
                                <div class="card-body">
                                    ${this.generateOverallAnalysis(products)}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // 生成并排比较表格
    generateSideBySideComparisonTable(products) {
        let table = `
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th style="width: 150px;">比较项目</th>
        `;

        // 表头 - 产品列
        products.forEach((product, index) => {
            table += `
                <th class="text-center">
                    <div class="product-header">
                        <div class="fw-bold">产品 ${index + 1}</div>
                        <small class="text-muted">${this.getPlatformName(product.source_platform)}</small>
                    </div>
                </th>
            `;
        });

        table += `
                        </tr>
                    </thead>
                    <tbody>
        `;

        // 产品标题行
        table += '<tr><td class="fw-bold bg-light">产品标题</td>';
        products.forEach(product => {
            table += `
                <td>
                    <div class="product-title-cell">
                        <a href="${this.escapeHtml(product.url)}" target="_blank" class="text-decoration-none">
                            ${this.escapeHtml(product.title.substring(0, 50))}${product.title.length > 50 ? '...' : ''}
                        </a>
                    </div>
                </td>
            `;
        });
        table += '</tr>';

        // 价格行
        table += '<tr><td class="fw-bold bg-light">价格</td>';
        const prices = products.map(p => p.min_price || 0);
        const minPrice = Math.min(...prices);
        products.forEach(product => {
            const price = product.min_price || 0;
            const isLowest = price === minPrice && price > 0;
            table += `
                <td class="text-center ${isLowest ? 'table-success' : ''}">
                    <div class="price-cell">
                        <span class="h5 ${isLowest ? 'text-success' : ''}">¥${price.toFixed(2)}</span>
                        ${isLowest && prices.filter(p => p > 0).length > 1 ? '<br><small class="badge bg-success">最低价</small>' : ''}
                        ${product.discount ? `<br><span class="badge bg-danger">-${product.discount}%</span>` : ''}
                    </div>
                </td>
            `;
        });
        table += '</tr>';

        // 销量行
        table += '<tr><td class="fw-bold bg-light">总销量</td>';
        const sales = products.map(p => p.total_sales || 0);
        const maxSales = Math.max(...sales);
        products.forEach(product => {
            const sale = product.total_sales || 0;
            const isHighest = sale === maxSales && sale > 0;
            table += `
                <td class="text-center ${isHighest ? 'table-success' : ''}">
                    <div class="sales-cell">
                        <span class="h5 ${isHighest ? 'text-success' : ''}">${sale.toLocaleString()}</span>
                        ${isHighest && sales.filter(s => s > 0).length > 1 ? '<br><small class="badge bg-success">最高销量</small>' : ''}
                        ${product.monthly_sales ? `<br><small class="text-muted">月销: ${product.monthly_sales}</small>` : ''}
                    </div>
                </td>
            `;
        });
        table += '</tr>';

        // 评分行
        table += '<tr><td class="fw-bold bg-light">评分</td>';
        const ratings = products.map(p => p.rating || 0);
        const maxRating = Math.max(...ratings);
        products.forEach(product => {
            const rating = product.rating || 0;
            const isHighest = rating === maxRating && rating > 0;
            table += `
                <td class="text-center ${isHighest ? 'table-success' : ''}">
                    <div class="rating-cell">
                        <div class="rating-stars mb-1">
                            ${this.generateStarRating(rating)}
                        </div>
                        <span class="h6 ${isHighest ? 'text-success' : ''}">${rating.toFixed(1)}</span>
                        ${isHighest && ratings.filter(r => r > 0).length > 1 ? '<br><small class="badge bg-success">最高评分</small>' : ''}
                        ${product.review_count ? `<br><small class="text-muted">${product.review_count}评价</small>` : ''}
                    </div>
                </td>
            `;
        });
        table += '</tr>';

        // 店铺信息行
        table += '<tr><td class="fw-bold bg-light">店铺信息</td>';
        products.forEach(product => {
            table += `
                <td>
                    <div class="shop-cell">
                        <div class="fw-bold">${this.escapeHtml(product.shop_name || '未知店铺')}</div>
                        ${product.shop_level ? `<span class="badge bg-info">${product.shop_level}</span>` : ''}
                    </div>
                </td>
            `;
        });
        table += '</tr>';

        // 分类行
        table += '<tr><td class="fw-bold bg-light">商品分类</td>';
        products.forEach(product => {
            table += `
                <td>
                    <small class="text-muted">${this.escapeHtml(product.category_path || '未分类')}</small>
                </td>
            `;
        });
        table += '</tr>';

        // 是否推广行
        table += '<tr><td class="fw-bold bg-light">推广状态</td>';
        products.forEach(product => {
            table += `
                <td class="text-center">
                    ${product.is_promoted ? 
                        '<span class="badge bg-warning">推广商品</span>' : 
                        '<span class="badge bg-secondary">普通商品</span>'
                    }
                </td>
            `;
        });
        table += '</tr>';

        table += `
                    </tbody>
                </table>
            </div>
        `;

        return table;
    }

    // 生成综合分析
    generateOverallAnalysis(products) {
        if (products.length === 0) return '<p class="text-muted">暂无数据</p>';

        const prices = products.map(p => p.min_price || 0).filter(p => p > 0);
        const sales = products.map(p => p.total_sales || 0);
        const ratings = products.map(p => p.rating || 0).filter(r => r > 0);

        let analysis = '';

        // 价格分析
        if (prices.length > 0) {
            const avgPrice = prices.reduce((a, b) => a + b, 0) / prices.length;
            const minPrice = Math.min(...prices);
            const maxPrice = Math.max(...prices);
            analysis += `
                <div class="mb-3">
                    <h6 class="text-primary">价格分析</h6>
                    <ul class="list-unstyled small">
                        <li>• 平均价格: ¥${avgPrice.toFixed(2)}</li>
                        <li>• 价格区间: ¥${minPrice.toFixed(2)} - ¥${maxPrice.toFixed(2)}</li>
                        <li>• 价格差异: ${((maxPrice - minPrice) / minPrice * 100).toFixed(1)}%</li>
                    </ul>
                </div>
            `;
        }

        // 销量分析
        if (sales.some(s => s > 0)) {
            const totalSales = sales.reduce((a, b) => a + b, 0);
            const avgSales = totalSales / sales.length;
            analysis += `
                <div class="mb-3">
                    <h6 class="text-success">销量分析</h6>
                    <ul class="list-unstyled small">
                        <li>• 总销量: ${totalSales.toLocaleString()}</li>
                        <li>• 平均销量: ${Math.round(avgSales).toLocaleString()}</li>
                    </ul>
                </div>
            `;
        }

        // 评分分析
        if (ratings.length > 0) {
            const avgRating = ratings.reduce((a, b) => a + b, 0) / ratings.length;
            analysis += `
                <div class="mb-3">
                    <h6 class="text-warning">评分分析</h6>
                    <ul class="list-unstyled small">
                        <li>• 平均评分: ${avgRating.toFixed(1)}/5.0</li>
                        <li>• 最高评分: ${Math.max(...ratings).toFixed(1)}</li>
                        <li>• 最低评分: ${Math.min(...ratings).toFixed(1)}</li>
                    </ul>
                </div>
            `;
        }

        // 推荐建议
        analysis += `
            <div class="mt-3 p-2 bg-light rounded">
                <h6 class="text-info">推荐建议</h6>
                <small class="text-muted">
                    ${this.generateRecommendations(products)}
                </small>
            </div>
        `;

        return analysis;
    }

    // 生成推荐建议
    generateRecommendations(products) {
        if (products.length === 0) return '暂无推荐';

        const recommendations = [];
        
        // 性价比推荐
        if (products.some(p => p.min_price > 0) && products.some(p => p.total_sales > 0)) {
            const bestValue = products
                .filter(p => p.min_price > 0 && p.total_sales > 0)
                .sort((a, b) => (b.total_sales / b.min_price) - (a.total_sales / a.min_price))[0];
            
            if (bestValue) {
                recommendations.push(`性价比推荐: ${bestValue.title.substring(0, 30)}...`);
            }
        }

        // 销量冠军
        const bestSeller = products.filter(p => p.total_sales > 0).sort((a, b) => b.total_sales - a.total_sales)[0];
        if (bestSeller) {
            recommendations.push(`销量冠军: ${bestSeller.title.substring(0, 30)}...`);
        }

        // 评分最高
        const bestRated = products.filter(p => p.rating > 0).sort((a, b) => b.rating - a.rating)[0];
        if (bestRated) {
            recommendations.push(`用户最爱: ${bestRated.title.substring(0, 30)}...`);
        }

        return recommendations.length > 0 ? recommendations.join('<br>') : '根据您的需求选择合适的产品';
    }

    // 初始化比较图表
    initializeComparisonCharts(products) {
        if (typeof Chart === 'undefined') {
            console.warn('Chart.js not loaded, skipping chart initialization');
            return;
        }

        // 价格对比图
        this.createPriceComparisonChart(products);
        
        // 销量对比图
        this.createSalesComparisonChart(products);
        
        // 评分对比图
        this.createRatingComparisonChart(products);
    }

    // 创建价格对比图表
    createPriceComparisonChart(products) {
        const ctx = document.getElementById('priceComparisonChart');
        if (!ctx) return;

        const data = {
            labels: products.map((p, i) => `产品${i + 1}`),
            datasets: [{
                label: '价格 (¥)',
                data: products.map(p => p.min_price || 0),
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 2,
                fill: true
            }]
        };

        new Chart(ctx, {
            type: 'bar',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '¥' + value.toFixed(2);
                            }
                        }
                    }
                }
            }
        });
    }

    // 创建销量对比图表
    createSalesComparisonChart(products) {
        const ctx = document.getElementById('salesComparisonChart');
        if (!ctx) return;

        const data = {
            labels: products.map((p, i) => `产品${i + 1}`),
            datasets: [{
                label: '销量',
                data: products.map(p => p.total_sales || 0),
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 2,
                fill: true
            }]
        };

        new Chart(ctx, {
            type: 'bar',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    }

    // 创建评分对比图表
    createRatingComparisonChart(products) {
        const ctx = document.getElementById('ratingComparisonChart');
        if (!ctx) return;

        const data = {
            labels: products.map((p, i) => `产品${i + 1}`),
            datasets: [{
                label: '评分',
                data: products.map(p => p.rating || 0),
                backgroundColor: 'rgba(255, 206, 86, 0.2)',
                borderColor: 'rgba(255, 206, 86, 1)',
                borderWidth: 2,
                fill: true
            }]
        };

        new Chart(ctx, {
            type: 'radar',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 5,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }

    // 导出比较结果
    exportComparison() {
        const productIds = Array.from(this.selectedProducts);
        const products = this.getSelectedProductsData(productIds);
        
        if (products.length === 0) {
            this.showToast('没有可导出的比较数据', 'warning');
            return;
        }

        // 生成CSV格式的比较数据
        let csvContent = '产品比较报告\n';
        csvContent += '生成时间,' + new Date().toLocaleString() + '\n\n';
        
        // 表头
        csvContent += '比较项目,' + products.map((p, i) => `产品${i + 1}`).join(',') + '\n';
        
        // 产品标题
        csvContent += '产品标题,' + products.map(p => `"${p.title}"`).join(',') + '\n';
        
        // 价格
        csvContent += '价格,' + products.map(p => (p.min_price || 0).toFixed(2)).join(',') + '\n';
        
        // 销量
        csvContent += '总销量,' + products.map(p => p.total_sales || 0).join(',') + '\n';
        
        // 评分
        csvContent += '评分,' + products.map(p => (p.rating || 0).toFixed(1)).join(',') + '\n';
        
        // 店铺
        csvContent += '店铺名称,' + products.map(p => `"${p.shop_name || ''}"`).join(',') + '\n';
        
        // 平台
        csvContent += '平台,' + products.map(p => this.getPlatformName(p.source_platform)).join(',') + '\n';
        
        // 分类
        csvContent += '分类,' + products.map(p => `"${p.category_path || ''}"`).join(',') + '\n';

        // 下载文件
        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const filename = `产品比较报告_${new Date().toISOString().slice(0, 10)}.csv`;
        this.downloadFile(blob, filename, 'text/csv');
        
        this.showToast('比较报告已导出', 'success');
    }

    generateCompareTable() {
        let table = `
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>属性</th>
        `;

        this.compareList.forEach((product, index) => {
            table += `<th>产品 ${index + 1}</th>`;
        });

        table += `
                        </tr>
                    </thead>
                    <tbody>
        `;

        // 产品标题行
        table += '<tr><td><strong>产品标题</strong></td>';
        this.compareList.forEach(product => {
            table += `<td><a href="${product.url || '#'}" target="_blank">${this.escapeHtml(product.title)}</a></td>`;
        });
        table += '</tr>';

        // 价格行
        table += '<tr><td><strong>价格</strong></td>';
        this.compareList.forEach(product => {
            table += `<td class="price-highlight">¥${(product.min_price || 0).toFixed(2)}</td>`;
        });
        table += '</tr>';

        // 销量行
        table += '<tr><td><strong>销量</strong></td>';
        this.compareList.forEach(product => {
            table += `<td>${(product.total_sales || 0).toLocaleString()}</td>`;
        });
        table += '</tr>';

        // 评分行
        table += '<tr><td><strong>评分</strong></td>';
        this.compareList.forEach(product => {
            table += `<td>${this.generateStarRating(product.rating || 0)} (${(product.rating || 0).toFixed(1)})</td>`;
        });
        table += '</tr>';

        // 平台行
        table += '<tr><td><strong>平台</strong></td>';
        this.compareList.forEach(product => {
            table += `<td><span class="badge bg-info">${this.getPlatformName(product.source_platform)}</span></td>`;
        });
        table += '</tr>';

        // 店铺行
        table += '<tr><td><strong>店铺</strong></td>';
        this.compareList.forEach(product => {
            table += `<td>${this.escapeHtml(product.shop_name || '未知店铺')}</td>`;
        });
        table += '</tr>';

        // 相关性评分行
        table += '<tr><td><strong>相关性评分</strong></td>';
        this.compareList.forEach(product => {
            const score = product.relevance_score || 0;
            const scoreColor = score >= 80 ? 'success' : score >= 60 ? 'warning' : 'secondary';
            table += `<td><span class="badge bg-${scoreColor}">${score.toFixed(1)}</span></td>`;
        });
        table += '</tr>';

        table += `
                    </tbody>
                </table>
            </div>
        `;

        return table;
    }

    clearCompareList() {
        this.compareList = [];
        this.updateCompareCounter();
        
        // 更新所有对比按钮状态
        this.searchResults.forEach(product => {
            this.updateCompareButton(product.id, false);
        });
        
        this.showSuccess('对比列表已清空');
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('compareModal'));
        if (modal) modal.hide();
    }

    addToWatchlist(productId) {
        const product = this.searchResults.find(p => p.id === productId);
        if (!product) {
            this.showError('产品不存在');
            return;
        }

        if (this.watchlist.find(p => p.id === productId)) {
            this.removeFromWatchlist(productId);
            return;
        }

        this.watchlist.push({
            id: product.id,
            title: product.title,
            price: product.min_price,
            platform: product.source_platform,
            shop_name: product.shop_name,
            url: product.url,
            added_at: new Date().toISOString()
        });

        localStorage.setItem('competitorWatchlist', JSON.stringify(this.watchlist));
        this.updateWatchlistButton(productId, true);
        this.showSuccess(`已添加 "${product.title}" 到关注列表`);
    }

    removeFromWatchlist(productId) {
        const index = this.watchlist.findIndex(p => p.id === productId);
        if (index > -1) {
            const product = this.watchlist[index];
            this.watchlist.splice(index, 1);
            localStorage.setItem('competitorWatchlist', JSON.stringify(this.watchlist));
            this.updateWatchlistButton(productId, false);
            this.showSuccess(`已从关注列表移除 "${product.title}"`);
        }
    }

    updateWatchlistButton(productId, added) {
        const button = document.querySelector(`button[onclick*="addToWatchlist(${productId})"]`);
        if (button) {
            if (added) {
                button.className = 'btn btn-warning btn-sm';
                button.innerHTML = '<i class="fas fa-star"></i>';
                button.title = '已关注';
            } else {
                button.className = 'btn btn-outline-warning btn-sm';
                button.innerHTML = '<i class="far fa-star"></i>';
                button.title = '关注';
            }
        }
    }

    clearForm() {
        document.getElementById('searchForm').reset();
        this.hideResults();
    }

    saveSearch() {
        const params = this.getSearchParams();
        const searchName = prompt('请输入搜索名称:');
        
        if (searchName) {
            const savedSearches = JSON.parse(localStorage.getItem('competitorSearches') || '[]');
            savedSearches.push({
                name: searchName,
                params: params,
                timestamp: new Date().toISOString()
            });
            localStorage.setItem('competitorSearches', JSON.stringify(savedSearches));
            this.showSuccess('搜索已保存');
        }
    }

    showExportModal() {
        if (this.searchResults.length === 0) {
            this.showError('没有可导出的搜索结果');
            return;
        }
        
        const modal = new bootstrap.Modal(document.getElementById('exportModal'));
        modal.show();
    }

    async exportResults() {
        if (!this.searchResults || this.searchResults.length === 0) {
            this.showError('没有可导出的搜索结果');
            return;
        }

        const format = document.getElementById('exportFormat').value;
        const includeImages = document.getElementById('includeImages').checked;

        const exportButton = document.getElementById('confirmExport');
        const originalText = exportButton.textContent;
        
        try {
            exportButton.disabled = true;
            exportButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 导出中...';

            const exportData = {
                results: this.searchResults,
                format: format,
                include_images: includeImages,
                search_params: this.currentSearchParams,
                export_time: new Date().toISOString()
            };

            const response = await fetch('/api/v1/competitors/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(exportData)
            });

            if (!response.ok) {
                throw new Error(`导出失败: HTTP ${response.status}`);
            }

            const blob = await response.blob();
            const filename = `competitor_search_${new Date().getTime()}.${format}`;
            
            this.downloadFile(blob, filename, response.headers.get('content-type'));
            this.showSuccess('导出成功');
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('exportModal'));
            modal.hide();

        } catch (error) {
            console.error('导出失败:', error);
            this.showError('导出失败: ' + error.message);
        } finally {
            exportButton.disabled = false;
            exportButton.textContent = originalText;
        }
    }

    downloadFile(content, filename, contentType) {
        const blob = content instanceof Blob ? content : new Blob([content], { type: contentType });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    }

    showLoading(show) {
        const indicator = document.getElementById('loadingIndicator');
        if (indicator) {
            indicator.style.display = show ? 'block' : 'none';
        }
        
        // 禁用/启用搜索按钮
        const searchButton = document.querySelector('button[type="submit"]');
        if (searchButton) {
            searchButton.disabled = show;
            if (show) {
                searchButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 搜索中...';
            } else {
                searchButton.innerHTML = '<i class="fas fa-search"></i> 搜索';
            }
        }
    }

    showResults() {
        document.getElementById('resultsCard').style.display = 'block';
    }

    hideResults() {
        document.getElementById('resultsCard').style.display = 'none';
    }

    showError(message) {
        this.showAlert(message, 'danger');
    }

    showSuccess(message) {
        this.showAlert(message, 'success');
    }

    showInfo(message) {
        this.showAlert(message, 'info');
    }

    showAlert(message, type = 'info', autoHide = true) {
        let alertContainer = document.getElementById('alertContainer');
        if (!alertContainer) {
            alertContainer = document.createElement('div');
            alertContainer.id = 'alertContainer';
            alertContainer.className = 'position-fixed top-0 end-0 p-3';
            alertContainer.style.zIndex = '9999';
            document.body.appendChild(alertContainer);
        }

        const alertId = 'alert-' + Date.now();
        const alertHtml = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="fas fa-${this.getAlertIcon(type)}"></i>
                ${this.escapeHtml(message)}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        alertContainer.insertAdjacentHTML('beforeend', alertHtml);

        if (autoHide && type !== 'danger') {
            setTimeout(() => {
                const alertElement = document.getElementById(alertId);
                if (alertElement) {
                    const alert = new bootstrap.Alert(alertElement);
                    alert.close();
                }
            }, 5000);
        }
    }

    getAlertIcon(type) {
        const icons = {
            'success': 'check-circle',
            'danger': 'exclamation-triangle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    hideAlert() {
        const alertContainer = document.getElementById('alertContainer');
        if (alertContainer) {
            alertContainer.innerHTML = '';
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// 初始化
let competitorSearch;
document.addEventListener('DOMContentLoaded', function() {
    competitorSearch = new CompetitorSearch();
});

// 全局函数，供HTML调用
function showAnalysisReport() {
    if (competitorSearch) {
        competitorSearch.showAnalysisReport();
    }
}

function exportAnalysis() {
    const modal = new bootstrap.Modal(document.getElementById('exportModal'));
    modal.show();
}

function exportAnalysisReport() {
    if (competitorSearch) {
        competitorSearch.exportAnalysisReport();
    }
}