@extends('layouts.app')

@section('title', '仪表板 - 电商市场动态监测系统')

@section('content')
<div class="container-fluid">
    <!-- 顶部统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-white bg-primary">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">监控任务</h4>
                            <h2 class="mb-0">{{ $taskCount ?? 0 }}</h2>
                            <p class="card-text">个活跃任务</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-tasks fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-success">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">监控商品</h4>
                            <h2 class="mb-0">{{ $productCount ?? 0 }}</h2>
                            <p class="card-text">个商品在监控</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-shopping-cart fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-warning">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">价格异常</h4>
                            <h2 class="mb-0">{{ $priceAlertCount ?? 0 }}</h2>
                            <p class="card-text">个价格异常警报</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-info">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">数据更新</h4>
                            <h2 class="mb-0">{{ $todayUpdateCount ?? 0 }}</h2>
                            <p class="card-text">今日更新次数</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-sync-alt fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="row">
        <!-- 左侧图表 -->
        <div class="col-lg-8">
            <!-- 价格趋势图 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        价格趋势分析
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="priceTrendChart" height="100"></canvas>
                </div>
            </div>

            <!-- 竞争对手促销分析 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        促销活动分布
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="promotionChart" height="80"></canvas>
                </div>
            </div>
        </div>

        <!-- 右侧信息面板 -->
        <div class="col-lg-4">
            <!-- 快速操作 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        快速操作
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('search') }}" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>
                            商品搜索
                        </a>
                        <a href="#" class="btn btn-success" onclick="showAddTaskModal()">
                            <i class="fas fa-plus me-2"></i>
                            新增监控任务
                        </a>
                        <a href="#" class="btn btn-info">
                            <i class="fas fa-download me-2"></i>
                            导出数据
                        </a>
                        <a href="#" class="btn btn-secondary">
                            <i class="fas fa-cog me-2"></i>
                            系统设置
                        </a>
                    </div>
                </div>
            </div>

            <!-- 最新警报 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bell me-2"></i>
                        最新警报
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        @forelse($recentAlerts ?? [] as $alert)
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">{{ $alert['title'] ?? '价格异常警报' }}</h6>
                                <small>{{ $alert['created_at'] ? $alert['created_at']->diffForHumans() : '刚刚' }}</small>
                            </div>
                            <p class="mb-1">{{ $alert['description'] ?? '监控数据异常' }}</p>
                            <small class="text-muted">{{ $alert['type'] ?? '系统消息' }}</small>
                        </div>
                        @empty
                        <div class="list-group-item text-center text-muted">
                            <i class="fas fa-check-circle me-2"></i>
                            暂无新警报
                        </div>
                        @endforelse
                    </div>
                </div>
            </div>

            <!-- 系统状态 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-server me-2"></i>
                        系统状态
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-success">正常</h4>
                                <small class="text-muted">数据采集</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">在线</h4>
                            <small class="text-muted">API服务</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-12">
                            <small class="text-muted">最后更新：</small>
                            <span class="text-dark">{{ now()->format('Y-m-d H:i:s') }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 新增任务模态框 -->
<div class="modal fade" id="addTaskModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">新增监控任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addTaskForm">
                    <div class="mb-3">
                        <label for="productName" class="form-label">商品名称</label>
                        <input type="text" class="form-control" id="productName" required>
                    </div>
                    <div class="mb-3">
                        <label for="productUrl" class="form-label">商品链接</label>
                        <input type="url" class="form-control" id="productUrl" required>
                    </div>
                    <div class="mb-3">
                        <label for="monitorFrequency" class="form-label">监控频率</label>
                        <select class="form-select" id="monitorFrequency">
                            <option value="hourly">每小时</option>
                            <option value="daily" selected>每天</option>
                            <option value="weekly">每周</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitAddTask()">添加任务</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.2/dist/chart.umd.min.js"></script>
    <script>
document.addEventListener('DOMContentLoaded', function() {
    // 价格趋势图
    const priceTrendCtx = document.getElementById('priceTrendChart').getContext('2d');
    const priceTrendChart = new Chart(priceTrendCtx, {
        type: 'line',
                        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                            datasets: [{
                label: '平均价格偏差率',
                data: [12, 19, 3, 5, 2, 3],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.1,
                fill: true
                            }]
                        },
                        options: {
                            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '价格偏差率 (%)'
                    }
                }
            }
        }
    });

    // 促销分布图
    const promotionCtx = document.getElementById('promotionChart').getContext('2d');
    const promotionChart = new Chart(promotionCtx, {
        type: 'doughnut',
                        data: {
            labels: ['满减', '折扣', '买赠', '拼团', '其他'],
            datasets: [{
                data: [35, 25, 20, 15, 5],
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF'
                ]
            }]
                        },
                        options: {
                            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                                }
                            }
                        }
                    });
                });

function showAddTaskModal() {
    const modal = new bootstrap.Modal(document.getElementById('addTaskModal'));
    modal.show();
}

function submitAddTask() {
    // 这里添加提交新任务的逻辑
    alert('新任务添加功能开发中...');
    const modal = bootstrap.Modal.getInstance(document.getElementById('addTaskModal'));
    modal.hide();
}
    </script>
@endsection 