@extends('layouts.app')

@section('title', '商品监控列表')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">首页</a></li>
    <li class="breadcrumb-item active">商品监控</li>
@endsection

@section('page-title', '商品监控列表')

@section('page-actions')
    <div class="btn-group">
        <a href="{{ route('data-sources.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>添加商品
        </a>
        <a href="{{ route('data-sources.excel-create') }}" class="btn btn-success">
            <i class="fas fa-file-excel me-1"></i>批量导入
        </a>
        <button type="button" class="btn btn-info" onclick="refreshData()">
            <i class="fas fa-sync-alt me-1"></i>刷新数据
        </button>
    </div>
@endsection

@section('content')
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>商品列表
                </h6>
            </div>
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" class="form-control" id="searchInput" placeholder="请输入商品名称或ID进行搜索...">
                    <button class="btn btn-outline-secondary" type="button" onclick="searchProducts()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card-body p-0">
        <!-- 筛选条件 -->
        <div class="row p-3 bg-light border-bottom">
            <div class="col-md-2">
                <select class="form-select form-select-sm" id="platformFilter">
                    <option value="">全部平台</option>
                    <option value="淘宝">淘宝</option>
                    <option value="天猫">天猫</option>
                    <option value="京东">京东</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select form-select-sm" id="statusFilter">
                    <option value="">全部状态</option>
                    <option value="1">已上架</option>
                    <option value="0">已下架</option>
                </select>
            </div>
            <div class="col-md-2">
                <input type="number" class="form-control form-control-sm" id="minPriceFilter" placeholder="最低价格">
            </div>
            <div class="col-md-2">
                <input type="number" class="form-control form-control-sm" id="maxPriceFilter" placeholder="最高价格">
            </div>
            <div class="col-md-2">
                <button class="btn btn-sm btn-primary" onclick="applyFilters()">
                    <i class="fas fa-filter me-1"></i>筛选
                </button>
            </div>
            <div class="col-md-2">
                <button class="btn btn-sm btn-secondary" onclick="clearFilters()">
                    <i class="fas fa-times me-1"></i>清除
                </button>
            </div>
        </div>
        
        <!-- 数据表格 -->
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="productsTable">
                <thead class="table-light">
                    <tr>
                        <th width="50">
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                        </th>
                        <th width="80">商品主图</th>
                        <th width="120">商品ID</th>
                        <th>商品标题</th>
                        <th width="100">价格</th>
                        <th width="80">库存</th>
                        <th width="100">店铺</th>
                        <th width="80">平台</th>
                        <th width="80">状态</th>
                        <th width="100">最小SKU价</th>
                        <th width="100">最大SKU价</th>
                        <th width="120">操作</th>
                    </tr>
                </thead>
                <tbody id="productsTableBody">
                    <!-- 数据将通过AJAX加载 -->
                    <tr>
                        <td colspan="12" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div class="mt-2">正在加载商品数据...</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        <div class="d-flex justify-content-between align-items-center p-3 border-top">
            <div class="text-muted">
                共 <span id="totalCount">0</span> 条，第 <span id="currentPage">1</span> 页，共 <span id="totalPages">0</span> 页
            </div>
            <nav>
                <ul class="pagination pagination-sm mb-0" id="pagination">
                    <!-- 分页按钮将通过JS生成 -->
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- 批量操作模态框 -->
<div class="modal fade" id="batchActionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>已选择 <span id="selectedCount">0</span> 个商品</p>
                <div class="d-grid gap-2">
                    <button class="btn btn-warning" onclick="batchUpdatePrices()">
                        <i class="fas fa-sync me-2"></i>批量更新价格
                    </button>
                    <button class="btn btn-info" onclick="batchExport()">
                        <i class="fas fa-download me-2"></i>导出选中商品
                    </button>
                    <button class="btn btn-danger" onclick="batchDelete()">
                        <i class="fas fa-trash me-2"></i>批量删除
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
let currentPage = 1;
let pageSize = 20;
let totalPages = 0;
let selectedProducts = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadProducts();
});

// 加载商品数据
function loadProducts(page = 1) {
    currentPage = page;
    
    // 构建查询参数
    const params = new URLSearchParams({
        page: page,
        per_page: pageSize,
        search: document.getElementById('searchInput').value,
        platform: document.getElementById('platformFilter').value,
        status: document.getElementById('statusFilter').value,
        min_price: document.getElementById('minPriceFilter').value,
        max_price: document.getElementById('maxPriceFilter').value
    });
    
    // 显示加载状态
    showLoading();
    
    // 模拟API调用 - 实际项目中替换为真实API
    setTimeout(() => {
        const mockData = generateMockData(page);
        renderProducts(mockData);
        renderPagination(mockData.pagination);
    }, 1000);
}

// 生成模拟数据
function generateMockData(page) {
    const products = [];
    const startIndex = (page - 1) * pageSize;
    
    for (let i = 1; i <= pageSize; i++) {
        const id = startIndex + i;
        products.push({
            id: `743${String(id).padStart(6, '0')}`,
            title: `商品标题 ${id} - 这是一个很长的商品标题用于测试显示效果`,
            image: 'https://via.placeholder.com/60x60',
            price: (Math.random() * 5000 + 100).toFixed(2),
            quantity: Math.floor(Math.random() * 1000),
            shop_name: `店铺${id % 10 + 1}`,
            platform: ['淘宝', '天猫', '京东'][id % 3],
            status: Math.random() > 0.2 ? 1 : 0,
            min_sku_price: (Math.random() * 1000 + 50).toFixed(2),
            max_sku_price: (Math.random() * 2000 + 500).toFixed(2)
        });
    }
    
    return {
        data: products,
        pagination: {
            current_page: page,
            per_page: pageSize,
            total: 2751,
            last_page: Math.ceil(2751 / pageSize)
        }
    };
}

// 显示加载状态
function showLoading() {
    document.getElementById('productsTableBody').innerHTML = `
        <tr>
            <td colspan="12" class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2">正在加载商品数据...</div>
            </td>
        </tr>
    `;
}

// 渲染商品列表
function renderProducts(response) {
    const tbody = document.getElementById('productsTableBody');
    const products = response.data;
    
    if (products.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="12" class="text-center py-4 text-muted">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <div>暂无商品数据</div>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = products.map(product => `
        <tr>
            <td>
                <input type="checkbox" class="product-checkbox" value="${product.id}" 
                       onchange="updateSelectedProducts()">
            </td>
            <td>
                <img src="${product.image}" alt="商品图片" class="img-thumbnail" width="50" height="50">
            </td>
            <td>
                <code>${product.id}</code>
            </td>
            <td>
                <div class="text-truncate" style="max-width: 300px;" title="${product.title}">
                    ${product.title}
                </div>
            </td>
            <td>
                <span class="text-success fw-bold">¥${product.price}</span>
            </td>
            <td>
                <span class="badge ${product.quantity > 0 ? 'bg-success' : 'bg-danger'}">
                    ${product.quantity}
                </span>
            </td>
            <td>
                <small>${product.shop_name}</small>
            </td>
            <td>
                <span class="badge bg-info">${product.platform}</span>
            </td>
            <td>
                <span class="badge ${product.status ? 'bg-success' : 'bg-secondary'}">
                    ${product.status ? '已上架' : '已下架'}
                </span>
            </td>
            <td>
                <small class="text-muted">¥${product.min_sku_price}</small>
            </td>
            <td>
                <small class="text-muted">¥${product.max_sku_price}</small>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary btn-sm" onclick="viewProduct('${product.id}')" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="updateProduct('${product.id}')" title="更新数据">
                        <i class="fas fa-sync"></i>
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="deleteProduct('${product.id}')" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// 渲染分页
function renderPagination(pagination) {
    totalPages = pagination.last_page;
    currentPage = pagination.current_page;

    // 更新统计信息
    document.getElementById('totalCount').textContent = pagination.total;
    document.getElementById('currentPage').textContent = currentPage;
    document.getElementById('totalPages').textContent = totalPages;

    // 生成分页按钮
    const paginationEl = document.getElementById('pagination');
    let paginationHTML = '';

    // 上一页
    if (currentPage > 1) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadProducts(${currentPage - 1})">上一页</a></li>`;
    }

    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    if (startPage > 1) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadProducts(1)">1</a></li>`;
        if (startPage > 2) {
            paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `<li class="page-item ${i === currentPage ? 'active' : ''}">
            <a class="page-link" href="#" onclick="loadProducts(${i})">${i}</a>
        </li>`;
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadProducts(${totalPages})">${totalPages}</a></li>`;
    }

    // 下一页
    if (currentPage < totalPages) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadProducts(${currentPage + 1})">下一页</a></li>`;
    }

    paginationEl.innerHTML = paginationHTML;
}

// 搜索商品
function searchProducts() {
    loadProducts(1);
}

// 应用筛选条件
function applyFilters() {
    loadProducts(1);
}

// 清除筛选条件
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('platformFilter').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('minPriceFilter').value = '';
    document.getElementById('maxPriceFilter').value = '';
    loadProducts(1);
}

// 刷新数据
function refreshData() {
    loadProducts(currentPage);
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.product-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateSelectedProducts();
}

// 更新选中的商品
function updateSelectedProducts() {
    const checkboxes = document.querySelectorAll('.product-checkbox:checked');
    selectedProducts = Array.from(checkboxes).map(cb => cb.value);

    // 更新全选状态
    const selectAll = document.getElementById('selectAll');
    const allCheckboxes = document.querySelectorAll('.product-checkbox');
    selectAll.checked = selectedProducts.length === allCheckboxes.length;
    selectAll.indeterminate = selectedProducts.length > 0 && selectedProducts.length < allCheckboxes.length;

    // 显示批量操作按钮
    if (selectedProducts.length > 0) {
        showBatchActions();
    } else {
        hideBatchActions();
    }
}

// 显示批量操作
function showBatchActions() {
    document.getElementById('selectedCount').textContent = selectedProducts.length;
    // 可以在这里显示批量操作工具栏
}

// 隐藏批量操作
function hideBatchActions() {
    // 隐藏批量操作工具栏
}

// 查看商品详情
function viewProduct(productId) {
    // 跳转到商品详情页面
    window.location.href = `/products/${productId}`;
}

// 更新商品数据
function updateProduct(productId) {
    if (confirm('确定要更新这个商品的数据吗？')) {
        // 调用更新API
        console.log('更新商品:', productId);
        // 实际项目中这里应该调用API
    }
}

// 删除商品
function deleteProduct(productId) {
    if (confirm('确定要删除这个商品吗？此操作不可恢复！')) {
        // 调用删除API
        console.log('删除商品:', productId);
        // 实际项目中这里应该调用API
    }
}

// 批量更新价格
function batchUpdatePrices() {
    if (selectedProducts.length === 0) {
        alert('请先选择要更新的商品');
        return;
    }

    if (confirm(`确定要更新选中的 ${selectedProducts.length} 个商品的价格吗？`)) {
        console.log('批量更新价格:', selectedProducts);
        // 实际项目中这里应该调用API
        bootstrap.Modal.getInstance(document.getElementById('batchActionModal')).hide();
    }
}

// 批量导出
function batchExport() {
    if (selectedProducts.length === 0) {
        alert('请先选择要导出的商品');
        return;
    }

    console.log('批量导出:', selectedProducts);
    // 实际项目中这里应该调用导出API
    bootstrap.Modal.getInstance(document.getElementById('batchActionModal')).hide();
}

// 批量删除
function batchDelete() {
    if (selectedProducts.length === 0) {
        alert('请先选择要删除的商品');
        return;
    }

    if (confirm(`确定要删除选中的 ${selectedProducts.length} 个商品吗？此操作不可恢复！`)) {
        console.log('批量删除:', selectedProducts);
        // 实际项目中这里应该调用API
        bootstrap.Modal.getInstance(document.getElementById('batchActionModal')).hide();
    }
}

// 键盘事件处理
document.addEventListener('keydown', function(e) {
    // Enter键搜索
    if (e.key === 'Enter' && e.target.id === 'searchInput') {
        searchProducts();
    }
});
</script>
<script src="{{ asset('js/api-client.js') }}"></script>
<script src="{{ asset('js/products.js') }}"></script>
@endsection
