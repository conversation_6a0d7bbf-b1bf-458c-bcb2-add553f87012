@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-file-excel text-success me-2"></i>
                        Excel批量导入数据源
                    </h4>
                    <a href="{{ route('data-sources.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回列表
                    </a>
                </div>

                <div class="card-body">
                    {{-- 使用说明 --}}
                    <div class="alert alert-info mb-4">
                        <h5 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>使用说明
                        </h5>
                        <ul class="mb-0">
                            <li>支持 <code>.xlsx</code>、<code>.xls</code>、<code>.csv</code> 格式文件</li>
                            <li>文件大小限制：10MB</li>
                            <li>第一行为表头，从第二行开始为数据</li>
                            <li>支持的平台：淘宝、天猫、京东、拼多多</li>
                            <li><strong>建议先下载模板，按照格式填写数据</strong></li>
                        </ul>
                    </div>

                    {{-- 下载模板 --}}
                    <div class="text-center mb-4">
                        <a href="{{ route('data-sources.download-template') }}" 
                           class="btn btn-success btn-lg">
                            <i class="fas fa-download me-2"></i>
                            下载Excel模板
                        </a>
                        <p class="text-muted mt-2">建议先下载模板，了解格式要求</p>
                    </div>

                    <hr class="my-4">

                    {{-- 上传表单 --}}
                    <form action="{{ route('data-sources.excel-store') }}" 
                          method="POST" 
                          enctype="multipart/form-data" 
                          id="excelForm">
                        @csrf

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="excel_file" class="form-label">
                                        <i class="fas fa-file-upload text-primary me-1"></i>
                                        选择Excel文件 <span class="text-danger">*</span>
                                    </label>
                                    <input type="file" 
                                           class="form-control @error('excel_file') is-invalid @enderror" 
                                           id="excel_file" 
                                           name="excel_file" 
                                           accept=".xlsx,.xls,.csv"
                                           required>
                                    @error('excel_file')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        支持格式：Excel (.xlsx, .xls) 和 CSV (.csv)，最大10MB
                                    </div>
                                </div>

                                {{-- 文件信息显示 --}}
                                <div id="fileInfo" class="alert alert-light d-none">
                                    <h6><i class="fas fa-file-alt me-1"></i>文件信息：</h6>
                                    <div id="fileDetails"></div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-cogs me-1"></i>导入选项
                                        </h6>
                                        
                                        <div class="form-check">
                                            <input class="form-check-input" 
                                                   type="checkbox" 
                                                   id="auto_monitor" 
                                                   name="auto_monitor" 
                                                   value="1">
                                            <label class="form-check-label" for="auto_monitor">
                                                自动监控价格变化
                                            </label>
                                            <div class="form-text">
                                                勾选后系统将定期监控商品价格变化
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {{-- 格式要求 --}}
                                <div class="card bg-light mt-3">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-list-alt me-1"></i>Excel格式要求
                                        </h6>
                                        <div class="small">
                                            <strong>第一列：</strong>商品URL或ID<br>
                                            <strong>第二列：</strong>平台（可选）<br>
                                            <strong>第三列：</strong>备注（可选）<br>
                                            <strong>第四列：</strong>自动监控（可选）
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- 提交按钮 --}}
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <button type="button" class="btn btn-secondary me-md-2" onclick="window.history.back()">
                                <i class="fas fa-times me-1"></i>取消
                            </button>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-upload me-1"></i>
                                <span class="btn-text">开始导入</span>
                                <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            {{-- 导入历史快捷链接 --}}
            <div class="text-center mt-3">
                <a href="{{ route('data-sources.import-history') }}" class="btn btn-outline-primary">
                    <i class="fas fa-history me-1"></i>查看导入历史
                </a>
            </div>
        </div>
    </div>
</div>

{{-- 进度模态框 --}}
<div class="modal fade" id="progressModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cog fa-spin me-2"></i>正在处理Excel文件...
                </h5>
            </div>
            <div class="modal-body text-center">
                <div class="progress mb-3">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         role="progressbar" style="width: 0%"></div>
                </div>
                <p class="mb-0">请耐心等待，处理大文件可能需要一些时间...</p>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('excel_file');
    const fileInfo = document.getElementById('fileInfo');
    const fileDetails = document.getElementById('fileDetails');
    const form = document.getElementById('excelForm');
    const submitBtn = document.getElementById('submitBtn');

    // 文件选择处理
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        
        if (file) {
            // 验证文件大小
            if (file.size > 10 * 1024 * 1024) { // 10MB
                alert('文件大小超过10MB限制！');
                fileInput.value = '';
                fileInfo.classList.add('d-none');
                return;
            }

            // 验证文件类型
            const allowedTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-excel',
                'text/csv'
            ];
            
            if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls|csv)$/i)) {
                alert('请选择有效的Excel或CSV文件！');
                fileInput.value = '';
                fileInfo.classList.add('d-none');
                return;
            }

            // 显示文件信息
            const sizeInMB = (file.size / (1024 * 1024)).toFixed(2);
            fileDetails.innerHTML = `
                <div class="row">
                    <div class="col-sm-4"><strong>文件名：</strong></div>
                    <div class="col-sm-8">${file.name}</div>
                </div>
                <div class="row">
                    <div class="col-sm-4"><strong>文件大小：</strong></div>
                    <div class="col-sm-8">${sizeInMB} MB</div>
                </div>
                <div class="row">
                    <div class="col-sm-4"><strong>最后修改：</strong></div>
                    <div class="col-sm-8">${new Date(file.lastModified).toLocaleString()}</div>
                </div>
            `;
            fileInfo.classList.remove('d-none');
        } else {
            fileInfo.classList.add('d-none');
        }
    });

    // 表单提交处理
    form.addEventListener('submit', function(e) {
        if (!fileInput.files[0]) {
            e.preventDefault();
            alert('请先选择要导入的Excel文件！');
            return;
        }

        // 显示加载状态
        submitBtn.disabled = true;
        submitBtn.querySelector('.btn-text').textContent = '正在导入...';
        submitBtn.querySelector('.spinner-border').classList.remove('d-none');
    });
});
</script>
@endpush

@push('styles')
<style>
.alert-light {
    border-left: 4px solid #0d6efd;
}

.form-check-label {
    cursor: pointer;
}

.btn:disabled {
    cursor: not-allowed;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}
</style>
@endpush
@endsection
