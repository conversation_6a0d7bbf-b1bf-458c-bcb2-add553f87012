<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 电商市场动态监测系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
        }
        .avatar-large {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid rgba(255, 255, 255, 0.2);
        }
        .stats-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .stats-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        .activity-item {
            border-left: 3px solid #e9ecef;
            padding-left: 1rem;
        }
        .activity-item.recent {
            border-left-color: #198754;
        }
        .sidebar {
            background: #f8f9fa;
            min-height: calc(100vh - 200px);
            border-radius: 10px;
        }
        .nav-link {
            color: #6c757d;
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .nav-link:hover, .nav-link.active {
            background: #667eea;
            color: white;
        }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ route('home') }}">
                <i class="bi bi-graph-up"></i> 电商监测系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ route('admin.dashboard') }}">
                    <i class="bi bi-speedometer2"></i> 管理后台
                </a>
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <img src="{{ auth()->user()->avatar_url }}" alt="头像" class="rounded-circle" width="24" height="24">
                        {{ auth()->user()->display_name }}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ route('profile.index') }}">个人中心</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form action="{{ route('logout') }}" method="POST" class="d-inline">
                                @csrf
                                <button type="submit" class="dropdown-item">退出登录</button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- 个人资料头部 -->
    <div class="profile-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="d-flex align-items-center">
                        <img src="{{ $user->avatar_url }}" alt="头像" class="avatar-large me-4">
                        <div>
                            <h2 class="mb-1">{{ $user->display_name }}</h2>
                            <p class="mb-2 opacity-75">
                                <i class="bi bi-envelope me-2"></i>{{ $user->email }}
                                @if($user->phone)
                                    <i class="bi bi-phone ms-3 me-2"></i>{{ $user->phone }}
                                @endif
                            </p>
                            @if($user->bio)
                                <p class="mb-0 opacity-75">{{ $user->bio }}</p>
                            @endif
                            @if($user->location || $user->website)
                                <p class="mb-0 opacity-75 small">
                                    @if($user->location)
                                        <i class="bi bi-geo-alt me-1"></i>{{ $user->location }}
                                    @endif
                                    @if($user->website)
                                        <i class="bi bi-globe ms-3 me-1"></i>
                                        <a href="{{ $user->website }}" class="text-white" target="_blank">个人网站</a>
                                    @endif
                                </p>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-md-end">
                    <a href="{{ route('profile.edit') }}" class="btn btn-outline-light">
                        <i class="bi bi-pencil-square"></i> 编辑资料
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3">
                <div class="sidebar p-3">
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="{{ route('profile.index') }}">
                            <i class="bi bi-house me-2"></i>个人中心
                        </a>
                        <a class="nav-link" href="{{ route('profile.edit') }}">
                            <i class="bi bi-person-gear me-2"></i>个人资料
                        </a>
                        <a class="nav-link" href="{{ route('profile.change-password') }}">
                            <i class="bi bi-shield-lock me-2"></i>修改密码
                        </a>
                        <a class="nav-link" href="{{ route('profile.preferences') }}">
                            <i class="bi bi-gear me-2"></i>偏好设置
                        </a>
                        <a class="nav-link" href="{{ route('profile.activity-log') }}">
                            <i class="bi bi-clock-history me-2"></i>活动日志
                        </a>
                    </nav>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-9">
                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="stats-card p-3 h-100">
                            <div class="d-flex align-items-center">
                                <div class="stats-icon bg-primary me-3">
                                    <i class="bi bi-list-task"></i>
                                </div>
                                <div>
                                    <h4 class="mb-0">{{ $stats['total_tasks'] }}</h4>
                                    <small class="text-muted">总任务数</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stats-card p-3 h-100">
                            <div class="d-flex align-items-center">
                                <div class="stats-icon bg-success me-3">
                                    <i class="bi bi-play-circle"></i>
                                </div>
                                <div>
                                    <h4 class="mb-0">{{ $stats['active_tasks'] }}</h4>
                                    <small class="text-muted">活跃任务</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stats-card p-3 h-100">
                            <div class="d-flex align-items-center">
                                <div class="stats-icon bg-info me-3">
                                    <i class="bi bi-check-circle"></i>
                                </div>
                                <div>
                                    <h4 class="mb-0">{{ $stats['completed_tasks'] }}</h4>
                                    <small class="text-muted">已完成</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stats-card p-3 h-100">
                            <div class="d-flex align-items-center">
                                <div class="stats-icon bg-warning me-3">
                                    <i class="bi bi-exclamation-triangle"></i>
                                </div>
                                <div>
                                    <h4 class="mb-0">{{ $stats['pending_alerts'] }}</h4>
                                    <small class="text-muted">待处理警报</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 账户信息和最近活动 -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-info-circle me-2"></i>账户信息
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-2">
                                    <div class="col-4 text-muted">用户名:</div>
                                    <div class="col-8">{{ $user->username }}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-4 text-muted">邮箱:</div>
                                    <div class="col-8">
                                        {{ $user->email }}
                                        @if($user->email_verified_at)
                                            <i class="bi bi-check-circle-fill text-success ms-1" title="已验证"></i>
                                        @else
                                            <i class="bi bi-x-circle-fill text-danger ms-1" title="未验证"></i>
                                        @endif
                                    </div>
                                </div>
                                @if($user->phone)
                                <div class="row mb-2">
                                    <div class="col-4 text-muted">手机:</div>
                                    <div class="col-8">
                                        {{ $user->phone }}
                                        @if($user->phone_verified_at)
                                            <i class="bi bi-check-circle-fill text-success ms-1" title="已验证"></i>
                                        @else
                                            <i class="bi bi-x-circle-fill text-danger ms-1" title="未验证"></i>
                                        @endif
                                    </div>
                                </div>
                                @endif
                                <div class="row mb-2">
                                    <div class="col-4 text-muted">角色:</div>
                                    <div class="col-8">
                                        @foreach($user->roles as $role)
                                            <span class="badge bg-primary me-1">{{ $role->display_name }}</span>
                                        @endforeach
                                    </div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-4 text-muted">注册时间:</div>
                                    <div class="col-8">{{ $stats['account_created']->format('Y-m-d H:i') }}</div>
                                </div>
                                @if($stats['last_login'])
                                <div class="row mb-2">
                                    <div class="col-4 text-muted">最后登录:</div>
                                    <div class="col-8">{{ $stats['last_login']->format('Y-m-d H:i') }}</div>
                                </div>
                                @endif
                                <div class="row">
                                    <div class="col-4 text-muted">登录次数:</div>
                                    <div class="col-8">{{ $stats['login_count'] }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-clock-history me-2"></i>最近活动
                                </h5>
                            </div>
                            <div class="card-body">
                                @if(count($recentActivities) > 0)
                                    @foreach($recentActivities as $activity)
                                        <div class="activity-item mb-3 {{ $loop->first ? 'recent' : '' }}">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <strong>{{ $activity['description'] }}</strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        IP: {{ $activity['ip'] }}
                                                    </small>
                                                </div>
                                                <small class="text-muted">
                                                    {{ $activity['created_at']->diffForHumans() }}
                                                </small>
                                            </div>
                                        </div>
                                    @endforeach
                                    <div class="text-center">
                                        <a href="{{ route('profile.activity-log') }}" class="btn btn-sm btn-outline-primary">
                                            查看全部活动
                                        </a>
                                    </div>
                                @else
                                    <div class="text-center text-muted py-3">
                                        <i class="bi bi-inbox display-4"></i>
                                        <p class="mt-2">暂无活动记录</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 成功/错误消息 -->
    @if(session('success'))
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div class="toast show" role="alert">
            <div class="toast-header bg-success text-white">
                <i class="bi bi-check-circle me-2"></i>
                <strong class="me-auto">成功</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                {{ session('success') }}
            </div>
        </div>
    </div>
    @endif

    @if(session('error'))
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div class="toast show" role="alert">
            <div class="toast-header bg-danger text-white">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <strong class="me-auto">错误</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                {{ session('error') }}
            </div>
        </div>
    </div>
    @endif

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 自动隐藏Toast消息
        setTimeout(function() {
            var toasts = document.querySelectorAll('.toast');
            toasts.forEach(function(toast) {
                var bsToast = new bootstrap.Toast(toast);
                bsToast.hide();
            });
        }, 5000);
    </script>
</body>
</html> 